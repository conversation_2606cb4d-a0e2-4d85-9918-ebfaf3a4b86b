<?xml version="1.0" encoding= "UTF-8" ?>
<configuration name="" xsi:schemaLocation="http://mcuxpresso.nxp.com/XSD/mex_configuration_15 http://mcuxpresso.nxp.com/XSD/mex_configuration_15.xsd" uuid="f38d8929-c9af-4240-90c4-409f557b857b" version="15" xmlns="http://mcuxpresso.nxp.com/XSD/mex_configuration_15" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <common>
      <processor>S32G399A</processor>
      <package>S32G399A_525bga</package>
      <mcu_data>$(release_id)</mcu_data>
      <cores selected="M7_0">
         <core name="Cortex-A53 (Core #0)" id="A53_0_0" description=""/>
         <core name="Cortex-A53 (Core #1)" id="A53_0_1" description=""/>
         <core name="Cortex-A53 (Core #2)" id="A53_1_0" description=""/>
         <core name="Cortex-A53 (Core #3)" id="A53_1_1" description=""/>
         <core name="Cortex-M7 (Core #4)" id="M7_0" description=""/>
         <core name="Cortex-M7 (Core #5)" id="M7_1" description=""/>
         <core name="Cortex-M7 (Core #6)" id="M7_2" description=""/>
         <core name="Cortex-M7 (Core #7)" id="M7_3" description=""/>
         <core name="Cortex-A53 (Core #8)" id="A53_0_2" description=""/>
         <core name="Cortex-A53 (Core #9)" id="A53_0_3" description=""/>
         <core name="Cortex-A53 (Core #10)" id="A53_1_2" description=""/>
         <core name="Cortex-A53 (Core #11)" id="A53_1_3" description=""/>
      </cores>
      <description></description>
   </common>
   <preferences>
      <validate_boot_init_only>true</validate_boot_init_only>
      <generate_extended_information>false</generate_extended_information>
      <generate_code_modified_registers_only>false</generate_code_modified_registers_only>
      <update_include_paths>true</update_include_paths>
      <generate_registers_defines>false</generate_registers_defines>
   </preferences>
   <tools>
      <pins name="Pins" version="13.1" enabled="false" update_project_code="true">
         <generated_project_files/>
         <pins_profile>
            <processor_version>0.0.0</processor_version>
            <power_domains/>
         </pins_profile>
         <functions_list>
            <function name="BOARD_InitPins">
               <description>Configures pin routing and optionally pin electrical features.</description>
               <options>
                  <callFromInitBoot>true</callFromInitBoot>
                  <coreID>M7_0</coreID>
               </options>
               <dependencies/>
               <pins/>
            </function>
         </functions_list>
      </pins>
      <clocks name="Clocks" version="13.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <clocks_profile>
            <processor_version>0.0.0</processor_version>
         </clocks_profile>
         <clock_configurations>
            <clock_configuration name="BOARD_BootClockRUN" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_REF, Clocks tool id: external_clocks.GMAC_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_RX_REF, Clocks tool id: external_clocks.GMAC_EXT_RX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TX_REF, Clocks tool id: external_clocks.GMAC_EXT_TX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS_REF, Clocks tool id: external_clocks.GMAC_EXT_TS_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_TX, Clocks tool id: external_clocks.PFEMAC0_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_RX, Clocks tool id: external_clocks.PFEMAC0_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC0_EXT_REF, Clocks tool id: external_clocks.PFEMAC0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_TX, Clocks tool id: external_clocks.PFEMAC1_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_RX, Clocks tool id: external_clocks.PFEMAC1_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC1_EXT_REF, Clocks tool id: external_clocks.PFEMAC1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_TX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_TX, Clocks tool id: external_clocks.PFEMAC2_EXT_TX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_RX" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_RX, Clocks tool id: external_clocks.PFEMAC2_EXT_RX) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".PFEMAC2_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .PFEMAC2_EXT_REF, Clocks tool id: external_clocks.PFEMAC2_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="FXOSC_CLK.FXOSC_CLK.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_0_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_1_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_rx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ts_ref.outFreq" value="200 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_tx_ref.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac0_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac1_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_rx.outFreq" value="125 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.pfemac2_ext_tx.outFreq" value="125 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="A53_CORE_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV10_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV2_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI0.outFreq" value="600 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI1.outFreq" value="300 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT0_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT1_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI0.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI1.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="CRC0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DAPB_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_PLL_PHI0.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA1_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC1_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM0_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM1_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM2_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM3_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="ENET_LOOPBACK_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="FIRCOUT.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXRAY_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERA_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERB_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FRAY0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_0_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_1_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FXOSCOUT.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_RX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TS_REF.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TX_REF.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC4_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST7_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LINFLEXD_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN_BAUD_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="OCOTP0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS1.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS2.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS3.outFreq" value="800 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS5.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI0.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI1.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI2.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI3.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI4.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI5.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI6.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI7.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PER_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC0_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC1_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_RX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_EXT_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFEMAC2_TX_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_PE_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="PFE_SYS_CLK.outFreq" value="12 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_1X_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="RTC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SDHC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_0_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_XPCS_1_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_0_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_XPCS_1_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIRCOUT.outFreq" value="32 kHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM0_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM1_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM2_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM4_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM5_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM6_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM7_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="USDHC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="WKPU0_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV2_CLK.outFreq" value="12 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV3_CLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV4_CLK.outFreq" value="6 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV6_CLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="ACCELPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="ACCELPLL_PHI1.scale" value="8" locked="true"/>
                  <setting id="ACCELPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="ACCELPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="ACCEL_MFD.scale" value="60" locked="true"/>
                  <setting id="ACCEL_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="CGM6_DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="COREPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS2.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS3.scale" value="4" locked="true"/>
                  <setting id="COREPLL_DFS4.scale" value="20/3" locked="true"/>
                  <setting id="COREPLL_DFS5.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_DFS6.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="COREPLL_PHI1.scale" value="20" locked="true"/>
                  <setting id="COREPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="COREPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="CORE_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_MFD.scale" value="50" locked="true"/>
                  <setting id="CORE_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DDRPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="DDRPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="DDR_MFD.scale" value="40" locked="false"/>
                  <setting id="DDR_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DDR_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_7_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_8_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_9_DE" value="Enabled" locked="false"/>
                  <setting id="FXOSC_PM" value="Crystal_mode" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX6_DIV0.scale" value="2" locked="true"/>
                  <setting id="MODULE_CLOCKS.RTC_CLK_MUX.sel" value="FIRC_CLK" locked="false"/>
                  <setting id="PERIPHPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS2.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS3.scale" value="2.5" locked="true"/>
                  <setting id="PERIPHPLL_DFS4.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS5.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_DFS6.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_MFD.scale" value="50" locked="true"/>
                  <setting id="PERIPHPLL_PHI0.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI1.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI2.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI3.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI4.scale" value="10" locked="true"/>
                  <setting id="PERIPHPLL_PHI5.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI6.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PHI7.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="PERIPH_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS2_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS3_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS5_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="PLLODIV0_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV1_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV2_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV3_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV4_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV5_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV6_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV7_DE" value="Enabled" locked="false"/>
                  <setting id="PREDIV.scale" value="1" locked="true"/>
               </clock_settings>
               <called_from_default_init>true</called_from_default_init>
            </clock_configuration>
         </clock_configurations>
      </clocks>
      <ddr name="DDR" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <components/>
      </ddr>
      <dcd name="DCD" version="1.0" enabled="true" update_project_code="true" isSelfTest="false">
         <generated_project_files>
            <file path="board/dcd_config.c" update_enabled="true"/>
         </generated_project_files>
         <dcdx_profile>
            <processor_version>N/A</processor_version>
         </dcdx_profile>
         <dcdx_configurations>
            <dcdx_configuration name="DCD Configuration">
               <description></description>
               <options/>
               <command_groups>
                  <command_group name="DCD Commands" enabled="true">
                     <commands/>
                  </command_group>
               </command_groups>
            </dcdx_configuration>
         </dcdx_configurations>
      </dcd>
      <ivt name="IVT" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/ivt_config.c" update_enabled="true"/>
         </generated_project_files>
         <ivt_profile>
            <processor_version>N/A</processor_version>
         </ivt_profile>
         <ivt_records>
            <ivt_pointers>
               <ivt_pointer id="" index="0" name="Self-Test DCD" size="4" start_address="0x100" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="1" name="Self-Test DCD (backup)" size="4" start_address="0x108" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="2" name="DCD" size="4" start_address="0x110" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="3" name="DCD (backup)" size="4" start_address="0x118" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="4" name="HSE" size="4" start_address="0x120" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="5" name="HSE (backup)" size="4" start_address="0x128" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="6" name="Application bootloader" size="4" start_address="0x130" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="7" name="Application bootloader (backup)" size="4" start_address="0x138" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
            </ivt_pointers>
            <ivt_image start_address="0x0" locked="true" sign_image="false">
               <custom_fields>
                  <custom_field name="gmac_iv_ivt_image" value="" disabled="true"/>
               </custom_fields>
            </ivt_image>
            <automatic_align start_address="0x0"/>
            <struct>
               <struct name="boot_config">
                  <setting>
                     <setting name="secured_boot" value="false"/>
                     <setting name="boot_watchdog" value="false"/>
                     <setting name="boot_target" value="A53_0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="gmac_generation">
                  <setting>
                     <setting name="random_gmac_iv" value="true"/>
                     <setting name="key_type" value="Plain ADKP"/>
                     <setting name="adkp_file" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="life_cycle_config">
                  <setting>
                     <setting name="life_cycle" value="Keep existing configuration"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="interface_selection">
                  <setting>
                     <setting name="QuadSPI_config_params" value="true"/>
                     <setting name="device_type" value="QuadSPI Serial Flash"/>
                     <setting name="quad_spi_params" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="hse_fw_config_parameters_struct">
                  <setting/>
                  <arrays/>
                  <child_structs>
                     <struct name="sys_img_pointer">
                        <setting>
                           <setting name="sys_img_pointer_addr" value="0x81000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_pointer_backup">
                        <setting>
                           <setting name="sys_img_pointer_backup_addr" value="0x8d000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_external_flash_type">
                        <setting>
                           <setting name="external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_flash_page_size">
                        <setting>
                           <setting name="flash_page_size" value="0x1000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="app_bsb_external_flash_type">
                        <setting>
                           <setting name="app_external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_marker">
                        <setting>
                           <setting name="vdd_marker" value="false"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_word">
                        <setting>
                           <setting name="io_polarity" value="GPIO low"/>
                           <setting name="gpio_mscr_value" value="0"/>
                           <setting name="delay_in_microseconds" value="0"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                  </child_structs>
               </struct>
            </struct>
            <ivt_flash image_path="" algorithm_name="" port=""/>
         </ivt_records>
      </ivt>
      <quadspi name="QuadSPI" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/quadspi_config.c" update_enabled="true"/>
         </generated_project_files>
         <quadspi_profile>
            <processor_version>N/A</processor_version>
         </quadspi_profile>
         <quadspi_records>
            <general_settings>
               <struct name="port_connection">
                  <setting>
                     <setting name="port" value="A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_bypass_mode">
                  <setting>
                     <setting name="dll_bypass_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_auto_upd_mode">
                  <setting>
                     <setting name="dll_auto_upd_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="ipcr_mode">
                  <setting>
                     <setting name="ipcr_trigger_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="sflash_clk_freq">
                  <setting>
                     <setting name="clk_freq" value="0x0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
            </general_settings>
            <quadspi_register name="MCR" value="0xf404c"/>
            <quadspi_register name="FLSHCR" value="0x303"/>
            <quadspi_register name="BFGENCR" value="0x0"/>
            <quadspi_register name="DLLCRA" value="0x1200000"/>
            <quadspi_register name="PARITYCR" value="0x0"/>
            <quadspi_register name="SFACR" value="0x800"/>
            <quadspi_register name="SMPR" value="0x0"/>
            <quadspi_register name="DLCR" value="0x40ff40ff"/>
            <quadspi_register name="SFA1AD" value="0x0"/>
            <quadspi_register name="SFA2AD" value="0x0"/>
            <quadspi_register name="DLPR" value="0xaa553443"/>
            <quadspi_register name="SFAR" value="0x0"/>
            <quadspi_register name="TBDR" value="0x0"/>
            <data_sequences>
               <struct name="command_sequences">
                  <setting/>
                  <arrays>
                     <array name="lut_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
               <struct name="flash_write_cmd">
                  <setting/>
                  <arrays>
                     <array name="flash_write_cmd_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
            </data_sequences>
         </quadspi_records>
      </quadspi>
      <efuse name="eFUSE" version="1.0" enabled="true" update_project_code="true">
         <efuse_profile>
            <processor_version>N/A</processor_version>
         </efuse_profile>
         <efuse_configuration>
            <fuse_words>
               <fuse_word id="boot_cfg1" name="BOOT_CFG1" value="0x20000000" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="boot_interface" name="Boot Interface" value="0x0"/>
                     <fuse_field id="dqs" name="DQS" value="0x1"/>
                     <fuse_field id="dllsmpfb" name="DLLSMPFB" value="0x0"/>
                     <fuse_field id="fsdly" name="Full Speed Delay" value="0x0"/>
                     <fuse_field id="fsphs" name="Full Speed Phase" value="0x0"/>
                     <fuse_field id="tdh" name="Time Hold Delay" value="0x0"/>
                     <fuse_field id="ckn" name="Differential Clock" value="0x0"/>
                     <fuse_field id="qspi_por_delay" name="QuadSPI POR Delay" value="0x0"/>
                     <fuse_field id="qspi_mode" name="QuadSPI Mode" value="0x0"/>
                     <fuse_field id="qspi_cas" name="Column Address Space" value="0x0"/>
                     <fuse_field id="ck2" name="CK2 Clock" value="0x0"/>
                     <fuse_field id="qspi_port" name="QuadSPI Port" value="0x0"/>
                     <fuse_field id="qspi_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="sd_speed" name="SD Speed" value="0x0"/>
                     <fuse_field id="sd_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="sd_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_boot_modes" name="MMC Boot Modes" value="0x0"/>
                     <fuse_field id="mmc_serial_rcon" name="Serial RCON Detection" value="0x0"/>
                     <fuse_field id="mmc_wait_period" name="Wait Period" value="0x0"/>
                     <fuse_field id="xosc_bypass_mode" name="XOSC Bypass Mode" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg2" name="BOOT_CFG2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="slv_dly_en" name="Slave Delay" value="0x0"/>
                     <fuse_field id="slv_dly_offset" name="Slave Delay Offset" value="0x0"/>
                     <fuse_field id="slv_dly_coarse" name="Slave Delay Coarse" value="0x0"/>
                     <fuse_field id="qspi_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="sd_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="mmc_fuse_sel" name="Fuse-based Boot" value="0x0"/>
                     <fuse_field id="dis_ser_boot" name="Disable Serial Boot" value="0x0"/>
                     <fuse_field id="xosc_configuration" name="XOSC Configuration" value="0x0"/>
                     <fuse_field id="xosc_mode" name="XOSC Mode" value="0x0"/>
                     <fuse_field id="xosc_gm_sel" name="Transconductance (GM_SEL)" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="boot_cfg3" name="BOOT_CFG3" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="qspi_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="qspi_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="qspi_pus_dqs" name="QSPI DQS Pad" value="0x0"/>
                     <fuse_field id="qspi_pus_data" name="QSPI Data Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_clk" name="QSPI Clock Pads" value="0x0"/>
                     <fuse_field id="qspi_pus_cs" name="QSPI CS Pads" value="0x0"/>
                     <fuse_field id="sd_pad_override_en" name="QSPI/SDHC Pad Override" value="0x0"/>
                     <fuse_field id="sd_sre" name="QSPI/SDHC Pad Slew Rate" value="0x0"/>
                     <fuse_field id="sd_pus_dqs" name="SDHC DQS Pad" value="0x0"/>
                     <fuse_field id="sd_pus_data" name="SDHC DATA Pads" value="0x0"/>
                     <fuse_field id="sd_pus_clk" name="SDHC Clock Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_cmd" name="SDHC CMD I/O Pad" value="0x0"/>
                     <fuse_field id="sdhc_pus_rst" name="SDHC RST Pads" value="0x0"/>
                     <fuse_field id="can_lin_pad_override_en" name="CAN/LIN Pad Override" value="0x0"/>
                     <fuse_field id="can_lin_sre" name="CAN/LIN Pad Slew Rate" value="0x0"/>
                     <fuse_field id="uart_rx_pus" name="UART RX PUS" value="0x0"/>
                     <fuse_field id="can_lin_rcvr" name="CAN/LIN RCVR" value="0x0"/>
                     <fuse_field id="uart_tx_pus" name="UART TX PUS" value="0x0"/>
                     <fuse_field id="can_tx_pus" name="CAN TX PUS" value="0x0"/>
                     <fuse_field id="can_rx_pus" name="CAN RX PUS" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="mac0_addr_31_0" name="MAC0_ADDR[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="mac0_addr_47_32" name="MAC0_ADDR[47:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp1" name="GP1" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp2" name="GP2" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_31_0" name="GP5[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_63_32" name="GP5[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_95_64" name="GP5[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_127_96" name="GP5[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_159_128" name="GP5[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp5_191_160" name="GP5[191:160]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_31_0" name="GP6[31:0]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_63_32" name="GP6[63:32]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_95_64" name="GP6[95:64]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_127_96" name="GP6[127:96]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="gp6_159_128" name="GP6[159:128]" value="0x0" ecc_protected="false">
                  <fuse_fields/>
               </fuse_word>
               <fuse_word id="lock_customer1" name="LOCK_BITS1" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="lock_customer_lock_bits" name="eFuse/Shadow Protection" value="0x0"/>
                     <fuse_field id="gp5_lock" name="GP5 Fuses Protection" value="0x0"/>
                     <fuse_field id="mac_addr_lock" name="MAC0_ADDR Fuses Protection" value="0x0"/>
                     <fuse_field id="boot_cfg_lock" name="BOOT ROM Fuses Protection" value="0x0"/>
                     <fuse_field id="gp2_lock" name="GP2 Fuse Protection" value="0x0"/>
                     <fuse_field id="gp1_lock" name="GP1 Fuse Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
               <fuse_word id="lock_customer2" name="LOCK_BITS2" value="0x0" ecc_protected="false">
                  <fuse_fields>
                     <fuse_field id="gp6_lock" name="GP6 Fuses Protection" value="0x0"/>
                  </fuse_fields>
               </fuse_word>
            </fuse_words>
         </efuse_configuration>
      </efuse>
      <gtm name="GTM" version="1.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <gtm_profile>
            <processor_version>N/A</processor_version>
         </gtm_profile>
      </gtm>
      <periphs name="Peripherals" version="14.0" enabled="true" update_project_code="true">
         <dependencies>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="osif is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="An unsupported version of the osif in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.adc" description="adc is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.adc" description="An unsupported version of the adc in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcl" description="mcl is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcl" description="An unsupported version of the mcl in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcu" description="Mcu is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.mcu" description="An unsupported version of the Mcu in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Platform" description="Platform is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.Platform" description="An unsupported version of the Platform in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="Tool" resourceId="Clocks" description="The Clocks tool is required by the Peripherals tool, but it is disabled." problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data>true</data>
               </feature>
            </dependency>
         </dependencies>
         <generated_project_files>
            <file path="generate/include/Adc_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Adc_Ipw_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_Ipw_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Adc_Ipw_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_Sar_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_Sar_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Adc_Sar_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/CDD_Mcl_Cfg.h" update_enabled="true"/>
            <file path="generate/include/CDD_Mcl_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/CDD_Mcl_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Cache_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Cache_Ip_Cfg_DeviceRegisters.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/DeviceDefinition.h" update_enabled="true"/>
            <file path="generate/include/Dma_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Dma_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Dma_Ip_Cfg_DeviceRegistersV3.h" update_enabled="true"/>
            <file path="generate/include/Dma_Ip_Cfg_Devices.h" update_enabled="true"/>
            <file path="generate/include/Dma_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Ftm_Mcl_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Ftm_Mcl_Ip_Cfg_DeviceRegisters.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Mcu_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Mcu_Ipw_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Mcu_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_ArchCfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Platform_Ipw_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Types.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Power_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Ram_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/SharedSettings_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Soc_Ips.h" update_enabled="true"/>
            <file path="generate/include/System_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/System_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/modules.h" update_enabled="true"/>
            <file path="generate/src/Adc_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Adc_Ipw_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Adc_Sar_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Adc_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/CDD_Mcl_Cfg.c" update_enabled="true"/>
            <file path="generate/src/CDD_Mcl_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Ctu_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Dma_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Dma_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/IntCtrl_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Mcu_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Mcu_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/OsIf_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Platform_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Platform_Ipw_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Power_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Power_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Ram_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Ram_Ip_VS_0_PBcfg.c" update_enabled="true"/>
         </generated_project_files>
         <peripherals_profile>
            <processor_version>0.0.0</processor_version>
            <ignored_component_migration_offer>
               <component>BaseNXP</component>
            </ignored_component_migration_offer>
         </peripherals_profile>
         <functional_groups>
            <functional_group name="VS_0" uuid="14e586ef-a787-4bec-aceb-5d36bd242bbe" called_from_default_init="true" id_prefix="" core="M7_0">
               <description></description>
               <options/>
               <dependencies/>
               <instances>
                  <instance name="BaseNXP" uuid="a62e3129-c7ba-41d9-97d3-6cb575e7e56b" type="BaseNXP" type_id="Base" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="BaseNXP">
                        <setting name="Name" value="BaseNXP"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="OsIfGeneral">
                           <setting name="Name" value="OsIfGeneral"/>
                           <setting name="OsIfMulticoreSupport" value="false"/>
                           <setting name="OsIfEnableUserModeSupport" value="false"/>
                           <setting name="OsIfDevErrorDetect" value="true"/>
                           <setting name="OsIfUseSystemTimer" value="false"/>
                           <setting name="OsIfUseCustomTimer" value="false"/>
                           <setting name="OsIfUseGetUserId" value="GET_CORE_ID"/>
                           <setting name="OsIfInstanceId" value="255"/>
                           <struct name="OsIfOperatingSystemType">
                              <setting name="Name" value="OsIfOperatingSystemType"/>
                              <setting name="Choice" value="OsIfBaremetalType"/>
                              <struct name="OsIfBaremetalType" quick_selection="Default">
                                 <setting name="Name" value="OsIfBaremetalType"/>
                              </struct>
                           </struct>
                           <array name="OsIfEcucPartitionRef"/>
                           <array name="OsIfCounterConfig"/>
                        </struct>
                        <struct name="CommonPublishedInformation" quick_selection="Default">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ModuleId" value="0"/>
                           <setting name="VendorId" value="43"/>
                           <array name="VendorApiInfix"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Adc" uuid="7dac2447-5328-4372-890d-9691c77c5e2e" type="Adc" type_id="Adc" mode="autosar" enabled="true" comment="" custom_name_enabled="true" editing_lock="false">
                     <config_set name="Adc">
                        <setting name="Name" value="Adc"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="true"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="AdcConfigSet">
                           <setting name="Name" value="AdcConfigSet"/>
                           <array name="AdcHwUnit">
                              <struct name="0">
                                 <setting name="Name" value="AdcHwUnit_0"/>
                                 <setting name="AdcHwUnitId" value="ADC0"/>
                                 <setting name="AdcLogicalUnitId" value="1"/>
                                 <setting name="AdcTransferType" value="ADC_INTERRUPT"/>
                                 <array name="AdcDmaChannelId"/>
                                 <array name="AdcCountingDmaChannelId"/>
                                 <array name="AdcClockSource"/>
                                 <array name="AdcPrescale">
                                    <setting name="0" value="1"/>
                                 </array>
                                 <setting name="AdcAltPrescale" value="2"/>
                                 <setting name="AdcCalibrationPrescale" value="2"/>
                                 <setting name="AdcPowerDownDelay" value="15"/>
                                 <setting name="AdcAltPowerDownDelay" value="15"/>
                                 <setting name="AdcAutoClockOff" value="false"/>
                                 <setting name="AdcBypassSampling" value="false"/>
                                 <setting name="AdcHwUnitOverwriteEn" value="true"/>
                                 <setting name="AdcPresamplingInternalSignal0" value="VREFH"/>
                                 <setting name="AdcPresamplingInternalSignal1" value="AVDD"/>
                                 <setting name="AdcHwUnitExtInjTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                                 <setting name="AdcHwUnitExtNrmTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                                 <setting name="AdcHwUnitPrimaryExtNrmTrg" value="false"/>
                                 <setting name="AdcHwUnitUsrOffset" value="0"/>
                                 <setting name="AdcHwUnitUsrGain" value="0"/>
                                 <setting name="AdcHwUnitDmaClearSource" value="DMA_REQ_CLEAR_ON_ACK"/>
                                 <array name="AdcSelfTestThresholdConfiguration">
                                    <struct name="0">
                                       <setting name="Name" value="AdcSelfTestThresholdConfiguration"/>
                                       <setting name="AdcSTAW0RSelfTestHighThresholdValue" value="2912"/>
                                       <setting name="AdcSTAW0RSelfTestLowThresholdValue" value="2367"/>
                                       <setting name="AdcSTAW1ARSelfTestHighThresholdValue" value="1"/>
                                       <setting name="AdcSTAW1ARSelfTestLowThresholdValue" value="1"/>
                                       <setting name="AdcSTAW1BRSelfTestHighThresholdValue" value="2867"/>
                                       <setting name="AdcSTAW1BRSelfTestLowThresholdValue" value="1414"/>
                                       <setting name="AdcSTAW2RSelfTestLowThresholdValue" value="4085"/>
                                       <setting name="AdcSTAW4RSelfTestHighThresholdValue" value="100"/>
                                       <setting name="AdcSTAW5RSelfTestHighThresholdValue" value="100"/>
                                    </struct>
                                 </array>
                                 <array name="AdcNormalConvTimings">
                                    <struct name="0">
                                       <setting name="Name" value="AdcNormalConvTimings"/>
                                       <setting name="AdcSamplingDurationNormal0" value="8"/>
                                       <setting name="AdcSamplingDurationNormal1" value="8"/>
                                    </struct>
                                 </array>
                                 <array name="AdcAlternateConvTimings">
                                    <struct name="0">
                                       <setting name="Name" value="AdcAlternateConvTimings"/>
                                       <setting name="AdcSamplingDurationAlt0" value="8"/>
                                       <setting name="AdcSamplingDurationAlt1" value="8"/>
                                    </struct>
                                 </array>
                                 <array name="AdcChannel">
                                    <struct name="0">
                                       <setting name="Name" value="Adc0Channel_1"/>
                                       <setting name="AdcLogicalChannelId" value="0"/>
                                       <setting name="AdcChannelName" value="ADC_CH_01_ChanNum1"/>
                                       <setting name="AdcChannelId" value="1"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="true"/>
                                       <array name="AdcThresholdRegister">
                                          <setting name="0" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/AdcThresholdControl_0"/>
                                       </array>
                                       <setting name="AdcWdogNotification" value="Notification_WDG_0"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="Adc0Channel_5"/>
                                       <setting name="AdcLogicalChannelId" value="1"/>
                                       <setting name="AdcChannelName" value="ADC_CH_05_ChanNum5"/>
                                       <setting name="AdcChannelId" value="5"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="true"/>
                                       <array name="AdcThresholdRegister">
                                          <setting name="0" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/AdcThresholdControl_1"/>
                                       </array>
                                       <setting name="AdcWdogNotification" value="Notification_WDG_1"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="Adc0_VREFL"/>
                                       <setting name="AdcLogicalChannelId" value="2"/>
                                       <setting name="AdcChannelName" value="VREFL_ChanNum37"/>
                                       <setting name="AdcChannelId" value="37"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="Adc0_VREFH"/>
                                       <setting name="AdcLogicalChannelId" value="3"/>
                                       <setting name="AdcChannelName" value="VREFH_ChanNum38"/>
                                       <setting name="AdcChannelId" value="38"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="Adc0_BANDGAP"/>
                                       <setting name="AdcLogicalChannelId" value="4"/>
                                       <setting name="AdcChannelName" value="BANDGAP_ChanNum32"/>
                                       <setting name="AdcChannelId" value="32"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="5">
                                       <setting name="Name" value="Adc0_AVDD"/>
                                       <setting name="AdcLogicalChannelId" value="5"/>
                                       <setting name="AdcChannelName" value="AVDD_ChanNum36"/>
                                       <setting name="AdcChannelId" value="36"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="6">
                                       <setting name="Name" value="Adc0_DVDD"/>
                                       <setting name="AdcLogicalChannelId" value="6"/>
                                       <setting name="AdcChannelName" value="DVDD_DIV2_ChanNum35"/>
                                       <setting name="AdcChannelId" value="35"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                 </array>
                                 <array name="AdcGroup">
                                    <struct name="0">
                                       <setting name="Name" value="AdcGroup_0"/>
                                       <setting name="AdcGroupAccessMode" value="ADC_ACCESS_MODE_SINGLE"/>
                                       <setting name="AdcGroupConversionMode" value="ADC_CONV_MODE_ONESHOT"/>
                                       <setting name="AdcGroupConversionType" value="ADC_CONV_TYPE_NORMAL"/>
                                       <setting name="AdcGroupId" value="0"/>
                                       <array name="AdcGroupPriority"/>
                                       <array name="AdcGroupReplacement"/>
                                       <setting name="AdcGroupTriggSrc" value="ADC_TRIGG_SRC_SW"/>
                                       <array name="AdcGroupHwTriggerSource"/>
                                       <array name="AdcHwTrigSignal"/>
                                       <array name="AdcHwTrigTimer">
                                          <setting name="0" value="0"/>
                                       </array>
                                       <array name="AdcNotification">
                                          <setting name="0" value="Notification_0"/>
                                       </array>
                                       <setting name="AdcExtraNotification" value="NULL_PTR"/>
                                       <setting name="AdcDmaErrorNotification" value=""/>
                                       <setting name="AdcStreamingBufferMode" value="ADC_STREAM_BUFFER_LINEAR"/>
                                       <setting name="AdcEnableOptimizeDmaStreamingGroups" value="false"/>
                                       <setting name="AdcEnableHalfInterrupt" value="false"/>
                                       <setting name="AdcStreamingNumSamples" value="1"/>
                                       <setting name="AdcStreamResultGroup" value="false"/>
                                       <setting name="AdcEnableChDisableChGroup" value="false"/>
                                       <setting name="AdcWithoutInterrupts" value="false"/>
                                       <setting name="AdcWithoutDma" value="false"/>
                                       <setting name="AdcExtDMAChanEnable" value="false"/>
                                       <struct name="AdcGroupConversionConfiguration">
                                          <setting name="Name" value="AdcGroupConversionConfiguration"/>
                                          <setting name="AdcSamplingDuration0" value="255"/>
                                          <setting name="AdcSamplingDuration1" value="255"/>
                                       </struct>
                                       <struct name="AdcAlternateGroupConvTimings">
                                          <setting name="Name" value="AdcAlternateGroupConvTimings"/>
                                          <setting name="AdcAltGroupSamplingDuration0" value="255"/>
                                          <setting name="AdcAltGroupSamplingDuration1" value="255"/>
                                       </struct>
                                       <array name="AdcGroupDefinition">
                                          <setting name="0" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_VREFL"/>
                                          <setting name="1" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_VREFH"/>
                                          <setting name="2" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_BANDGAP"/>
                                          <setting name="3" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_AVDD"/>
                                          <setting name="4" value="/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_DVDD"/>
                                       </array>
                                       <array name="AdcGroupEcucPartitionRef"/>
                                    </struct>
                                 </array>
                                 <array name="AdcThresholdControl">
                                    <struct name="0">
                                       <setting name="Name" value="AdcThresholdControl_0"/>
                                       <setting name="AdcThresholdControlRegister" value="ADC_THRESHOLD_REG_1"/>
                                       <array name="AdcHighThreshold">
                                          <setting name="0" value="4095"/>
                                       </array>
                                       <array name="AdcLowThreshold">
                                          <setting name="0" value="0"/>
                                       </array>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="AdcThresholdControl_1"/>
                                       <setting name="AdcThresholdControlRegister" value="ADC_THRESHOLD_REG_0"/>
                                       <array name="AdcHighThreshold">
                                          <setting name="0" value="4095"/>
                                       </array>
                                       <array name="AdcLowThreshold">
                                          <setting name="0" value="0"/>
                                       </array>
                                    </struct>
                                 </array>
                                 <array name="AdcHwUnitEcucPartitionRef"/>
                              </struct>
                              <struct name="1">
                                 <setting name="Name" value="AdcHwUnitDma"/>
                                 <setting name="AdcHwUnitId" value="ADC1"/>
                                 <setting name="AdcLogicalUnitId" value="0"/>
                                 <setting name="AdcTransferType" value="ADC_DMA"/>
                                 <array name="AdcDmaChannelId">
                                    <setting name="0" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                 </array>
                                 <array name="AdcCountingDmaChannelId"/>
                                 <array name="AdcClockSource"/>
                                 <array name="AdcPrescale"/>
                                 <setting name="AdcAltPrescale" value="2"/>
                                 <setting name="AdcCalibrationPrescale" value="2"/>
                                 <setting name="AdcPowerDownDelay" value="15"/>
                                 <setting name="AdcAltPowerDownDelay" value="15"/>
                                 <setting name="AdcAutoClockOff" value="false"/>
                                 <setting name="AdcBypassSampling" value="false"/>
                                 <setting name="AdcHwUnitOverwriteEn" value="true"/>
                                 <setting name="AdcPresamplingInternalSignal0" value="DVDD"/>
                                 <setting name="AdcPresamplingInternalSignal1" value="DVDD"/>
                                 <setting name="AdcHwUnitExtInjTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                                 <setting name="AdcHwUnitExtNrmTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                                 <setting name="AdcHwUnitPrimaryExtNrmTrg" value="false"/>
                                 <setting name="AdcHwUnitUsrOffset" value="0"/>
                                 <setting name="AdcHwUnitUsrGain" value="0"/>
                                 <setting name="AdcHwUnitDmaClearSource" value="DMA_REQ_CLEAR_ON_ACK"/>
                                 <array name="AdcSelfTestThresholdConfiguration">
                                    <struct name="0">
                                       <setting name="Name" value="AdcSelfTestThresholdConfiguration"/>
                                       <setting name="AdcSTAW0RSelfTestHighThresholdValue" value="2912"/>
                                       <setting name="AdcSTAW0RSelfTestLowThresholdValue" value="2367"/>
                                       <setting name="AdcSTAW1ARSelfTestHighThresholdValue" value="1"/>
                                       <setting name="AdcSTAW1ARSelfTestLowThresholdValue" value="1"/>
                                       <setting name="AdcSTAW1BRSelfTestHighThresholdValue" value="2867"/>
                                       <setting name="AdcSTAW1BRSelfTestLowThresholdValue" value="1414"/>
                                       <setting name="AdcSTAW2RSelfTestLowThresholdValue" value="4085"/>
                                       <setting name="AdcSTAW4RSelfTestHighThresholdValue" value="100"/>
                                       <setting name="AdcSTAW5RSelfTestHighThresholdValue" value="100"/>
                                    </struct>
                                 </array>
                                 <array name="AdcNormalConvTimings"/>
                                 <array name="AdcAlternateConvTimings"/>
                                 <array name="AdcChannel">
                                    <struct name="0">
                                       <setting name="Name" value="Adc1Channel_1"/>
                                       <setting name="AdcLogicalChannelId" value="0"/>
                                       <setting name="AdcChannelName" value="ADC_CH_08_ChanNum0"/>
                                       <setting name="AdcChannelId" value="0"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="Adc1Channel_5"/>
                                       <setting name="AdcLogicalChannelId" value="1"/>
                                       <setting name="AdcChannelName" value="ADC_CH_09_ChanNum1"/>
                                       <setting name="AdcChannelId" value="1"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="Adc1_VREFH"/>
                                       <setting name="AdcLogicalChannelId" value="2"/>
                                       <setting name="AdcChannelName" value="VREFH_ChanNum38"/>
                                       <setting name="AdcChannelId" value="38"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="Adc1_VREFL"/>
                                       <setting name="AdcLogicalChannelId" value="3"/>
                                       <setting name="AdcChannelName" value="VREFL_ChanNum37"/>
                                       <setting name="AdcChannelId" value="37"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="Adc1_BANDGAP"/>
                                       <setting name="AdcLogicalChannelId" value="4"/>
                                       <setting name="AdcChannelName" value="BANDGAP_ChanNum32"/>
                                       <setting name="AdcChannelId" value="32"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="5">
                                       <setting name="Name" value="Adc1_DVDD"/>
                                       <setting name="AdcLogicalChannelId" value="5"/>
                                       <setting name="AdcChannelName" value="DVDD_DIV2_ChanNum35"/>
                                       <setting name="AdcChannelId" value="35"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="6">
                                       <setting name="Name" value="Adc1_AVDD"/>
                                       <setting name="AdcLogicalChannelId" value="6"/>
                                       <setting name="AdcChannelName" value="AVDD_ChanNum36"/>
                                       <setting name="AdcChannelId" value="36"/>
                                       <array name="AdcChannelConvTime"/>
                                       <array name="AdcChannelLimitCheck"/>
                                       <array name="AdcChannelHighLimit"/>
                                       <array name="AdcChannelLowLimit"/>
                                       <array name="AdcChannelRangeSelect"/>
                                       <array name="AdcChannelRefVoltsrcHigh"/>
                                       <array name="AdcChannelRefVoltsrcLow"/>
                                       <array name="AdcChannelResolution"/>
                                       <array name="AdcChannelSampTime"/>
                                       <setting name="AdcEnablePresampling" value="false"/>
                                       <setting name="AdcEnableThresholds" value="false"/>
                                       <array name="AdcThresholdRegister"/>
                                       <setting name="AdcWdogNotification" value="NULL_PTR"/>
                                    </struct>
                                 </array>
                                 <array name="AdcGroup">
                                    <struct name="0">
                                       <setting name="Name" value="AdcSwGroupWithDma"/>
                                       <setting name="AdcGroupAccessMode" value="ADC_ACCESS_MODE_SINGLE"/>
                                       <setting name="AdcGroupConversionMode" value="ADC_CONV_MODE_ONESHOT"/>
                                       <setting name="AdcGroupConversionType" value="ADC_CONV_TYPE_NORMAL"/>
                                       <setting name="AdcGroupId" value="1"/>
                                       <array name="AdcGroupPriority"/>
                                       <array name="AdcGroupReplacement"/>
                                       <setting name="AdcGroupTriggSrc" value="ADC_TRIGG_SRC_SW"/>
                                       <array name="AdcGroupHwTriggerSource"/>
                                       <array name="AdcHwTrigSignal"/>
                                       <array name="AdcHwTrigTimer"/>
                                       <array name="AdcNotification">
                                          <setting name="0" value="Notification_1"/>
                                       </array>
                                       <setting name="AdcExtraNotification" value="NULL_PTR"/>
                                       <setting name="AdcDmaErrorNotification" value=""/>
                                       <setting name="AdcStreamingBufferMode" value="ADC_STREAM_BUFFER_LINEAR"/>
                                       <setting name="AdcEnableOptimizeDmaStreamingGroups" value="false"/>
                                       <setting name="AdcEnableHalfInterrupt" value="false"/>
                                       <setting name="AdcStreamingNumSamples" value="1"/>
                                       <setting name="AdcStreamResultGroup" value="false"/>
                                       <setting name="AdcEnableChDisableChGroup" value="false"/>
                                       <setting name="AdcWithoutInterrupts" value="false"/>
                                       <setting name="AdcWithoutDma" value="false"/>
                                       <setting name="AdcExtDMAChanEnable" value="false"/>
                                       <struct name="AdcGroupConversionConfiguration">
                                          <setting name="Name" value="AdcGroupConversionConfiguration"/>
                                          <setting name="AdcSamplingDuration0" value="255"/>
                                          <setting name="AdcSamplingDuration1" value="255"/>
                                       </struct>
                                       <struct name="AdcAlternateGroupConvTimings">
                                          <setting name="Name" value="AdcAlternateGroupConvTimings"/>
                                          <setting name="AdcAltGroupSamplingDuration0" value="255"/>
                                          <setting name="AdcAltGroupSamplingDuration1" value="255"/>
                                       </struct>
                                       <array name="AdcGroupDefinition">
                                          <setting name="0" value="/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_VREFL"/>
                                          <setting name="1" value="/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_VREFH"/>
                                          <setting name="2" value="/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_BANDGAP"/>
                                          <setting name="3" value="/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_AVDD"/>
                                          <setting name="4" value="/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_DVDD"/>
                                       </array>
                                       <array name="AdcGroupEcucPartitionRef"/>
                                    </struct>
                                 </array>
                                 <array name="AdcThresholdControl"/>
                                 <array name="AdcHwUnitEcucPartitionRef"/>
                              </struct>
                           </array>
                           <array name="AdcHwTrigger">
                              <struct name="0">
                                 <setting name="Name" value="AdcHwTrigger_1"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_ODD_3_FTM1_CH1"/>
                              </struct>
                              <struct name="1">
                                 <setting name="Name" value="AdcHwTrigger_2"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_ODD_1_FTM0_CH2"/>
                              </struct>
                              <struct name="2">
                                 <setting name="Name" value="AdcHwTrigger_4"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_REL_FTM0_CH0"/>
                              </struct>
                              <struct name="3">
                                 <setting name="Name" value="AdcHwTrigger_5"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_ODD_0_FTM0_CH1"/>
                              </struct>
                              <struct name="4">
                                 <setting name="Name" value="AdcHwTrigger_6"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_ODD_2_FTM1_CH0"/>
                              </struct>
                              <struct name="5">
                                 <setting name="Name" value="AdcHwTrigger_7"/>
                                 <setting name="AdcHwTrigSrc" value="CTU_PWM_EVEN_0_FTM1_CH2"/>
                              </struct>
                           </array>
                           <array name="CtuHwUnit">
                              <struct name="0">
                                 <setting name="Name" value="CtuHwUnit_0"/>
                                 <setting name="CtuHwUnitId" value="0"/>
                                 <setting name="CtuLogicalUnitId" value="0"/>
                                 <setting name="CtuTGSMode" value="TGS_MODE_SEQUENTIAL"/>
                                 <setting name="CtuInputClockPrescaler" value="PRESCALER_1"/>
                                 <setting name="CtuExtTrigMode" value="EXT_TRIG_MODE_PULSE"/>
                                 <setting name="CtuTgsCounterCompareVal" value="256"/>
                                 <setting name="CtuTgsCounterReloadVal" value="0"/>
                                 <setting name="CtuAdcCmdListMode" value="ADC_CMD_LIST_MODE_STREAMING"/>
                                 <setting name="CtuSeqModeMrsInput" value="PWM_REL_FTM0_CH0"/>
                                 <setting name="CtuSeqModeMrsInputEdge" value="EDGE_BOTH"/>
                                 <setting name="CtuDmaDoneGRE" value="false"/>
                                 <setting name="CtuDmaReqMRS" value="false"/>
                                 <setting name="CtuFifoDmaRawData" value="false"/>
                                 <setting name="CtuDisableOutput" value="false"/>
                                 <setting name="CtuErrorNotif" value="NULL_PTR"/>
                                 <setting name="CtuMrsNotif" value="NULL_PTR"/>
                                 <setting name="CtuAdcCmdIssueNotif" value="NULL_PTR"/>
                                 <setting name="CtuDigitalFilter" value="0"/>
                                 <setting name="CtuControlOnTime" value="0"/>
                                 <struct name="ExpectedConvDurationConfig">
                                    <setting name="Name" value="ExpectedConvDurationConfig"/>
                                    <setting name="CtuExpectedValuePortA" value="65535"/>
                                    <setting name="CtuExpectedNotifPortA" value="NULL_PTR"/>
                                    <setting name="CtuExpectedValuePortB" value="65535"/>
                                    <setting name="CtuExpectedNotifPortB" value="NULL_PTR"/>
                                    <setting name="CtuConvDurationCounterRange" value="65535"/>
                                 </struct>
                                 <array name="CtuInputTrigConfigs">
                                    <struct name="0">
                                       <setting name="Name" value="CtuInputTrigConfigs_0"/>
                                       <setting name="CtuInputTrigSelect" value="/Adc/Adc/AdcConfigSet/AdcHwTrigger_4"/>
                                       <setting name="CtuInputTrigEdge" value="EDGE_RISING"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="CtuInputTrigConfigs_1"/>
                                       <setting name="CtuInputTrigSelect" value="/Adc/Adc/AdcConfigSet/AdcHwTrigger_6"/>
                                       <setting name="CtuInputTrigEdge" value="EDGE_FALLING"/>
                                    </struct>
                                 </array>
                                 <array name="CtuTriggerCfg">
                                    <struct name="0">
                                       <setting name="Name" value="CtuTriggerCfg_0"/>
                                       <setting name="CtuTriggerIndex" value="0"/>
                                       <setting name="CtuCompareVal" value="10"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="65"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="CtuTriggerCfg_1"/>
                                       <setting name="CtuTriggerIndex" value="1"/>
                                       <setting name="CtuCompareVal" value="100"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="1"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="CtuTriggerCfg_2"/>
                                       <setting name="CtuTriggerIndex" value="2"/>
                                       <setting name="CtuCompareVal" value="200"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="0"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="CtuTriggerCfg_3"/>
                                       <setting name="CtuTriggerIndex" value="3"/>
                                       <setting name="CtuCompareVal" value="300"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="64"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="CtuTriggerCfg_4"/>
                                       <setting name="CtuTriggerIndex" value="4"/>
                                       <setting name="CtuCompareVal" value="400"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="1"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="5">
                                       <setting name="Name" value="CtuTriggerCfg_5"/>
                                       <setting name="CtuTriggerIndex" value="5"/>
                                       <setting name="CtuCompareVal" value="500"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="0"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="6">
                                       <setting name="Name" value="CtuTriggerCfg_6"/>
                                       <setting name="CtuTriggerIndex" value="6"/>
                                       <setting name="CtuCompareVal" value="600"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="0"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="7">
                                       <setting name="Name" value="CtuTriggerCfg_7"/>
                                       <setting name="CtuTriggerIndex" value="7"/>
                                       <setting name="CtuCompareVal" value="700"/>
                                       <setting name="CtuCmdListStartAdr" value="0"/>
                                       <setting name="CtuOutputTrigEnMask" value="0"/>
                                       <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                    </struct>
                                 </array>
                                 <array name="CtuAdcCommandList">
                                    <struct name="0">
                                       <setting name="Name" value="CtuAdcCommandList_0"/>
                                       <setting name="CtuIntEn" value="false"/>
                                       <setting name="CtuFifoIdx" value="0"/>
                                       <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                       <setting name="CtuLastCmd" value="NOT_LAST"/>
                                       <setting name="CtuAdcPort" value="ADC_0"/>
                                       <setting name="CtuAdcChanA" value="ADC_CH_01_ChanNum1"/>
                                       <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="CtuAdcCommandList_1"/>
                                       <setting name="CtuIntEn" value="false"/>
                                       <setting name="CtuFifoIdx" value="0"/>
                                       <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                       <setting name="CtuLastCmd" value="NOT_LAST"/>
                                       <setting name="CtuAdcPort" value="ADC_0"/>
                                       <setting name="CtuAdcChanA" value="ADC_CH_01_ChanNum1"/>
                                       <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="CtuAdcCommandList_2"/>
                                       <setting name="CtuIntEn" value="false"/>
                                       <setting name="CtuFifoIdx" value="0"/>
                                       <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                       <setting name="CtuLastCmd" value="NOT_LAST"/>
                                       <setting name="CtuAdcPort" value="ADC_0"/>
                                       <setting name="CtuAdcChanA" value="ADC_CH_01_ChanNum1"/>
                                       <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="CtuAdcCommandList_3"/>
                                       <setting name="CtuIntEn" value="false"/>
                                       <setting name="CtuFifoIdx" value="0"/>
                                       <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                       <setting name="CtuLastCmd" value="NOT_LAST"/>
                                       <setting name="CtuAdcPort" value="ADC_0"/>
                                       <setting name="CtuAdcChanA" value="ADC_CH_05_ChanNum5"/>
                                       <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="CtuAdcCommandList_4"/>
                                       <setting name="CtuIntEn" value="false"/>
                                       <setting name="CtuFifoIdx" value="0"/>
                                       <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                       <setting name="CtuLastCmd" value="LAST"/>
                                       <setting name="CtuAdcPort" value="ADC_0"/>
                                       <setting name="CtuAdcChanA" value="ADC_CH_02_ChanNum2"/>
                                       <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                    </struct>
                                 </array>
                                 <array name="CtuResultFifos">
                                    <struct name="0">
                                       <setting name="Name" value="CtuResultFifos_0"/>
                                       <setting name="CtuFifoIndex" value="0"/>
                                       <setting name="CtuFifoThreshold" value="3"/>
                                       <setting name="CtuFifoDmaEn" value="false"/>
                                       <setting name="CtuFifoDmaBuffer" value="CtuDmaFifo0"/>
                                       <array name="CtuFifoDmaChannelId"/>
                                       <setting name="CtuFifoThresholdNotif" value="Fifo0ThresholdNotification"/>
                                       <setting name="CtuFifoUnderrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoOverrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoFullNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="CtuResultFifos_1"/>
                                       <setting name="CtuFifoIndex" value="1"/>
                                       <setting name="CtuFifoThreshold" value="3"/>
                                       <setting name="CtuFifoDmaEn" value="false"/>
                                       <setting name="CtuFifoDmaBuffer" value="CtuDmaFifo1"/>
                                       <array name="CtuFifoDmaChannelId"/>
                                       <setting name="CtuFifoThresholdNotif" value="Fifo1ThresholdNotification"/>
                                       <setting name="CtuFifoUnderrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoOverrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoFullNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="CtuResultFifos_2"/>
                                       <setting name="CtuFifoIndex" value="2"/>
                                       <setting name="CtuFifoThreshold" value="8"/>
                                       <setting name="CtuFifoDmaEn" value="false"/>
                                       <setting name="CtuFifoDmaBuffer" value="CtuDmaFifo2"/>
                                       <array name="CtuFifoDmaChannelId"/>
                                       <setting name="CtuFifoThresholdNotif" value="Fifo2ThresholdNotification"/>
                                       <setting name="CtuFifoUnderrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoOverrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoFullNotif" value="NULL_PTR"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="CtuResultFifos_3"/>
                                       <setting name="CtuFifoIndex" value="3"/>
                                       <setting name="CtuFifoThreshold" value="8"/>
                                       <setting name="CtuFifoDmaEn" value="false"/>
                                       <setting name="CtuFifoDmaBuffer" value="CtuDmaFifo3"/>
                                       <array name="CtuFifoDmaChannelId"/>
                                       <setting name="CtuFifoThresholdNotif" value="Fifo3ThresholdNotification"/>
                                       <setting name="CtuFifoUnderrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoOverrunNotif" value="NULL_PTR"/>
                                       <setting name="CtuFifoFullNotif" value="NULL_PTR"/>
                                    </struct>
                                 </array>
                              </struct>
                           </array>
                        </struct>
                        <struct name="AdcGeneral">
                           <setting name="Name" value="AdcGeneral"/>
                           <setting name="AdcDeInitApi" value="true"/>
                           <setting name="AdcDevErrorDetect" value="true"/>
                           <setting name="AdcEnableLimitCheck" value="true"/>
                           <setting name="AdcEnableQueuing" value="true"/>
                           <setting name="AdcPriorityQueueMaxDepth" value="1"/>
                           <setting name="AdcEnableStartStopGroupApi" value="true"/>
                           <setting name="AdcGrpNotifCapability" value="true"/>
                           <setting name="AdcHwTriggerApi" value="true"/>
                           <setting name="AdcPriorityImplementation" value="ADC_PRIORITY_NONE"/>
                           <setting name="AdcReadGroupApi" value="true"/>
                           <setting name="AdcResultAlignment" value="ADC_ALIGN_RIGHT"/>
                           <setting name="AdcVersionInfoApi" value="true"/>
                           <array name="AdcLowPowerStatesSupport">
                              <setting name="0" value="true"/>
                           </array>
                           <array name="AdcPowerStateAsynchTransitionMode"/>
                           <array name="AdcEcucPartitionRef"/>
                           <array name="AdcKernelEcucPartitionRef"/>
                           <array name="AdcPowerStateConfig"/>
                        </struct>
                        <array name="AdcHwConfiguration">
                           <struct name="0">
                              <setting name="Name" value="AdcHwConfiguration_0"/>
                              <setting name="AdcHwConfiguredId" value="ADC0"/>
                              <setting name="AdcNormalInterruptEnable" value="true"/>
                              <setting name="AdcInjectedInterruptEnable" value="true"/>
                              <setting name="CtuFifoOfInterruptEnable" value="true"/>
                              <setting name="WdgThresholdEnable" value="true"/>
                              <setting name="DmaTransferEnable" value="false"/>
                           </struct>
                           <struct name="1">
                              <setting name="Name" value="AdcHwConfiguration_1"/>
                              <setting name="AdcHwConfiguredId" value="ADC1"/>
                              <setting name="AdcNormalInterruptEnable" value="false"/>
                              <setting name="AdcInjectedInterruptEnable" value="false"/>
                              <setting name="CtuFifoOfInterruptEnable" value="false"/>
                              <setting name="WdgThresholdEnable" value="false"/>
                              <setting name="DmaTransferEnable" value="true"/>
                           </struct>
                        </array>
                        <struct name="AdcPublishedInformation">
                           <setting name="Name" value="AdcPublishedInformation"/>
                           <setting name="AdcChannelValueSigned" value="false"/>
                           <setting name="AdcGroupFirstChannelFixed" value="false"/>
                           <setting name="AdcMaxChannelResolution" value="12"/>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="ModuleId" value="123"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                           <setting name="VendorApiInfix" value=""/>
                           <setting name="VendorId" value="43"/>
                        </struct>
                        <struct name="AutosarExt">
                           <setting name="Name" value="AutosarExt"/>
                           <setting name="AdcTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="AdcTimeoutVal" value="65536"/>
                           <setting name="AdcSarIpDevErrorDetect" value="false"/>
                           <setting name="CtuIpDevErrorDetect" value="false"/>
                           <setting name="AdcMulticoreSupport" value="false"/>
                           <setting name="AdcMaxPartitions" value="1"/>
                           <setting name="AdcEnableGroupDependentChannelNames" value="false"/>
                           <setting name="AdcBypassAbortChainCheck" value="false"/>
                           <setting name="AdcConvTimeOnce" value="false"/>
                           <setting name="AdcOptimizeOneShotHwTriggerConversions" value="false"/>
                           <setting name="AdcOptimizeDmaStreamingGroups" value="false"/>
                           <setting name="AdcPreSamplingOnce" value="true"/>
                           <setting name="AdcEnableInitialNotification" value="false"/>
                           <setting name="AdcEnableDmaTransferMode" value="true"/>
                           <setting name="AdcEnableDmaErrorNotification" value="false"/>
                           <setting name="AdcUseSoftwareInjectedGroups" value="false"/>
                           <setting name="AdcUseHardwareNormalGroups" value="false"/>
                           <setting name="AdcEnableUserModeSupport" value="false"/>
                           <setting name="AdcSetHwUnitPowerModeApi" value="false"/>
                           <setting name="AdcEnableChDisableChApi" value="false"/>
                           <setting name="AdcGetInjectedConvStatusApi" value="false"/>
                           <setting name="AdcEnableThresholdConfigurationApi" value="false"/>
                           <setting name="AdcEnableCtuTrigAutosarExtApi" value="false"/>
                           <setting name="AdcCtuHardwareTriggerOptimization" value="false"/>
                           <setting name="AdcEnableCtuControlModeApi" value="true"/>
                           <setting name="CtuEnableDmaTransferMode" value="false"/>
                           <setting name="AdcCtuControlModeExtraApis" value="false"/>
                           <setting name="AdcEnableWatchdogApi" value="true"/>
                           <setting name="AdcEnableSetChannel" value="false"/>
                           <setting name="AdcEnableDualClockMode" value="false"/>
                           <setting name="AdcEnableCalibration" value="true"/>
                           <setting name="AdcEnableAsyncCalibration" value="false"/>
                           <setting name="AdcEnableSelfTest" value="false"/>
                           <setting name="AdcEnableReadRawDataApi" value="false"/>
                           <setting name="AdcEnableGroupStreamingResultReorder" value="false"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="EcuC" uuid="66cb479d-6725-46ea-892f-6d5529b7bc5d" type="EcuC" type_id="EcuC" mode="general" enabled="true" comment="" custom_name_enabled="true" editing_lock="false">
                     <config_set name="EcuC">
                        <setting name="Name" value="EcuC"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-POST-BUILD"/>
                        </struct>
                        <array name="EcucPduCollection">
                           <struct name="0">
                              <setting name="Name" value="EcucPduCollection_0"/>
                              <array name="Pdu">
                                 <struct name="0">
                                    <setting name="Name" value="Pdu_0"/>
                                    <setting name="EcucPduId" value="0"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                        <array name="EcucPartitionCollection">
                           <struct name="0">
                              <setting name="Name" value="EcucPartitionCollection_0"/>
                              <array name="EcucPartition">
                                 <struct name="0">
                                    <setting name="Name" value="EcucPartition_0"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="1">
                                    <setting name="Name" value="EcucPartition_One"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="2">
                                    <setting name="Name" value="EcucPartition_2"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="3">
                                    <setting name="Name" value="EcucPartition_3"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="4">
                                    <setting name="Name" value="EcucPartition_4"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="5">
                                    <setting name="Name" value="EcucPartition_Cinque"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="6">
                                    <setting name="Name" value="EcucPartition_6"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                                 <struct name="7">
                                    <setting name="Name" value="EcucPartition_7"/>
                                    <setting name="EcucPartitionBswModuleExecution" value="true"/>
                                    <setting name="EcucPartitionQmBswModuleExecution" value="true"/>
                                    <setting name="PartitionCanBeRestarted" value="true"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                        <array name="EcucHardware">
                           <struct name="0">
                              <setting name="Name" value="EcucHardware_0"/>
                              <array name="EcucCoreDefinition">
                                 <struct name="0">
                                    <setting name="Name" value="EcucCoreDefinition_0"/>
                                    <setting name="EcucCoreId" value="0"/>
                                 </struct>
                                 <struct name="1">
                                    <setting name="Name" value="EcucCoreDefinition_1"/>
                                    <setting name="EcucCoreId" value="1"/>
                                 </struct>
                                 <struct name="2">
                                    <setting name="Name" value="EcucCoreDefinition_2"/>
                                    <setting name="EcucCoreId" value="2"/>
                                 </struct>
                                 <struct name="3">
                                    <setting name="Name" value="EcucCoreDefinition_3"/>
                                    <setting name="EcucCoreId" value="3"/>
                                 </struct>
                                 <struct name="4">
                                    <setting name="Name" value="EcucCoreDefinition_4"/>
                                    <setting name="EcucCoreId" value="4"/>
                                 </struct>
                                 <struct name="5">
                                    <setting name="Name" value="EcucCoreDefinition_5"/>
                                    <setting name="EcucCoreId" value="5"/>
                                 </struct>
                                 <struct name="6">
                                    <setting name="Name" value="EcucCoreDefinition_6"/>
                                    <setting name="EcucCoreId" value="6"/>
                                 </struct>
                                 <struct name="7">
                                    <setting name="Name" value="EcucCoreDefinition_7"/>
                                    <setting name="EcucCoreId" value="7"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                        <array name="EcucPostBuildVariants">
                           <struct name="0">
                              <setting name="Name" value="EcucPostBuildVariants"/>
                              <array name="EcucPostBuildVariantRef">
                                 <setting name="0" value="/system/SystemModel/PostBuildSelectable/BOARD_InitPeripherals"/>
                              </array>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
                  <instance name="Mcl" uuid="d3ca6b1a-01d8-4d62-b66f-95e34f0d8f11" type="Mcl" type_id="Mcl" mode="autosar" enabled="true" comment="" custom_name_enabled="true" editing_lock="false">
                     <config_set name="Mcl">
                        <setting name="Name" value="Mcl"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="true"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="MclGeneral">
                           <setting name="Name" value="MclGeneral"/>
                           <setting name="MclEnableDevErrorDetect" value="true"/>
                           <setting name="Mcl_VersionInfoApi" value="false"/>
                           <setting name="MclEnableUserModeSupport" value="false"/>
                           <setting name="MclEnableMulticoreSupport" value="false"/>
                           <setting name="MclEnableVirtualAddressMappingSupport" value="false"/>
                           <struct name="MclDma">
                              <setting name="Name" value="MclDma"/>
                              <setting name="MclEnableDma" value="true"/>
                              <setting name="MclEnableCrc" value="false"/>
                           </struct>
                           <struct name="MclCache">
                              <setting name="Name" value="MclCache"/>
                              <setting name="MclEnableCache" value="true"/>
                           </struct>
                           <struct name="MclFtmCommon">
                              <setting name="Name" value="MclFtmCommon"/>
                              <setting name="Mcl_FtmCommonTimebase" value="false"/>
                           </struct>
                        </struct>
                        <struct name="MclConfig">
                           <setting name="Name" value="MclConfig"/>
                           <array name="MclVirtualMemorySection"/>
                           <array name="dmaLogicInstance_ConfigType">
                              <struct name="0">
                                 <setting name="Name" value="dmaLogicInstance_ConfigType_0"/>
                                 <setting name="dmaLogicInstance_IdName" value="DMA_LOGIC_INST_0"/>
                                 <setting name="dmaLogicInstance_hwId" value="DMA_IP_HW_INST_0"/>
                                 <setting name="dmaLogicInstance_enDebug" value="false"/>
                                 <setting name="dmaLogicInstance_enRoundRobin" value="false"/>
                                 <setting name="dmaLogicInstance_enHaltAfterError" value="false"/>
                                 <setting name="dmaLogicInstance_enChLinking" value="false"/>
                                 <setting name="dmaLogicInstance_enGlMasterIdReplication" value="false"/>
                                 <setting name="dmaLogicInstance_EcucPartitionRef" value="/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0"/>
                                 <struct name="dmaLogicInstance_DmaCrc">
                                    <setting name="Name" value="dmaLogicInstance_DmaCrc"/>
                                    <setting name="dmaLogicInstance_DmaCrcEnSwapBit" value="false"/>
                                    <setting name="dmaLogicInstance_DmaCrcEnSwapByte" value="false"/>
                                    <setting name="dmaLogicInstance_DmaCrcEnGlobal" value="false"/>
                                 </struct>
                              </struct>
                           </array>
                           <array name="dmaLogicChannel_Type">
                              <struct name="0">
                                 <setting name="Name" value="CHANNEL_FOR_ADC1"/>
                                 <setting name="dmaLogicChannel_LogicName" value="DMA_LOGIC_CH_0"/>
                                 <setting name="dmaLogicChannel_HwInstId" value="DMA_IP_HW_INST_0"/>
                                 <setting name="dmaLogicChannel_HwChId" value="DMA_IP_HW_CH_16"/>
                                 <setting name="dmaLogicChannel_InterruptCallback" value="Adc_Ipw_Adc1DmaTransferCompleteNotification"/>
                                 <setting name="dmaLogicChannel_ErrorInterruptCallback" value="NULL_PTR"/>
                                 <setting name="dmaLogicChannel_EcucPartitionRef" value="/EcuC/EcuC/EcucPartitionCollection_0/EcucPartition_0"/>
                                 <setting name="dmaLogicChannel_EnableGlobalConfig" value="true"/>
                                 <setting name="dmaLogicChannel_EnableTransferConfig" value="false"/>
                                 <setting name="dmaLogicChannel_EnableScatterGather" value="true"/>
                                 <setting name="dmaLogicChannel_EnableCrcConfig" value="false"/>
                                 <struct name="dmaLogicChannel_ConfigType">
                                    <setting name="Name" value="dmaLogicChannel_ConfigType"/>
                                    <struct name="dmaLogicChannel_GlobalConfigType">
                                       <setting name="Name" value="dmaLogicChannel_GlobalConfigType"/>
                                       <struct name="dmaLogicChannelConfig_GlobalControlType">
                                          <setting name="Name" value="dmaLogicChannelConfig_GlobalControlType"/>
                                          <setting name="dmaGlobalControl_enMasterIdReplication" value="false"/>
                                          <setting name="dmaGlobalControl_enBufferedWrites" value="false"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_GlobalRequestType">
                                          <setting name="Name" value="dmaLogicChannelConfig_GlobalRequestType"/>
                                          <setting name="dmaGlobalRequest_enDmamuxTrigger" value="false"/>
                                          <setting name="dmaGlobalRequest_enDmamuxSource" value="true"/>
                                          <setting name="dmaGlobalRequest_Dmamux0HwRequest" value="DMA_IP_REQ_MUX0_DISABLED"/>
                                          <setting name="dmaGlobalRequest_Dmamux1HwRequest" value="DMA_IP_REQ_MUX1_SARADC1"/>
                                          <setting name="dmaGlobalRequest_Dmamux2HwRequest" value="DMA_IP_REQ_MUX2_DISABLED"/>
                                          <setting name="dmaGlobalRequest_Dmamux3HwRequest" value="DMA_IP_REQ_MUX3_DISABLED"/>
                                          <setting name="dmaGlobalRequest_enDmaRequest" value="false"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_GlobalInterruptType">
                                          <setting name="Name" value="dmaLogicChannelConfig_GlobalInterruptType"/>
                                          <setting name="dmaGlobalInterrupt_enDmaErrorInterrupt" value="false"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_GlobalPriorityType">
                                          <setting name="Name" value="dmaLogicChannelConfig_GlobalPriorityType"/>
                                          <setting name="dmaGlobalPriority_GroupPriority" value="DMA_IP_GROUP_PRIO0"/>
                                          <setting name="dmaGlobalPriority_LevelPriority" value="DMA_IP_LEVEL_PRIO0"/>
                                          <setting name="dmaGlobalPriority_enPreemption" value="false"/>
                                          <setting name="dmaGlobalPriority_disPreempt" value="false"/>
                                       </struct>
                                    </struct>
                                    <struct name="dmaLogicChannel_TransferConfigType">
                                       <setting name="Name" value="dmaLogicChannel_TransferConfigType"/>
                                       <struct name="dmaLogicChannelConfig_TransferControlType">
                                          <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                          <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                          <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                          <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                          <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                          <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_TransferSourceType">
                                          <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                          <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                          <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                          <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                          <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                          <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                          <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                          <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                          <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                          <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                          <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                          <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                          <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                          <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                          <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                          <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                          <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                       </struct>
                                       <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                          <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                          <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                          <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                          <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                       </struct>
                                    </struct>
                                    <struct name="dmaLogicChannel_ScatterGatherConfigType">
                                       <setting name="Name" value="dmaLogicChannel_ScatterGatherConfigType"/>
                                       <array name="dmaLogicChannelConfig_ScatterGatherArrayType">
                                          <struct name="0">
                                             <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherArrayType_0"/>
                                             <struct name="dmaLogicChannelConfig_ScatterGatherElementConfigType">
                                                <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherElementConfigType"/>
                                                <setting name="dmaLogicChannelConfig_ScatterGatherElementNameType" value="DMA_LOGIC_CH_0_SGA_ELEMENT_0"/>
                                                <setting name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" value="false"/>
                                                <setting name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_1"/>
                                                <setting name="dmaLogicChannelConfig_enScatterGatherConfig" value="false"/>
                                                <struct name="dmaLogicChannelConfig_TransferControlType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                                   <setting name="dmaLogicChannelConfig_enStart" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferSourceType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                                   <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                                </struct>
                                             </struct>
                                          </struct>
                                          <struct name="1">
                                             <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherArrayType_1"/>
                                             <struct name="dmaLogicChannelConfig_ScatterGatherElementConfigType">
                                                <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherElementConfigType"/>
                                                <setting name="dmaLogicChannelConfig_ScatterGatherElementNameType" value="DMA_LOGIC_CH_0_SGA_ELEMENT_1"/>
                                                <setting name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" value="false"/>
                                                <setting name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_2"/>
                                                <setting name="dmaLogicChannelConfig_enScatterGatherConfig" value="false"/>
                                                <struct name="dmaLogicChannelConfig_TransferControlType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                                   <setting name="dmaLogicChannelConfig_enStart" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferSourceType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                                   <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                                </struct>
                                             </struct>
                                          </struct>
                                          <struct name="2">
                                             <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherArrayType_2"/>
                                             <struct name="dmaLogicChannelConfig_ScatterGatherElementConfigType">
                                                <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherElementConfigType"/>
                                                <setting name="dmaLogicChannelConfig_ScatterGatherElementNameType" value="DMA_LOGIC_CH_0_SGA_ELEMENT_2"/>
                                                <setting name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" value="false"/>
                                                <setting name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_3"/>
                                                <setting name="dmaLogicChannelConfig_enScatterGatherConfig" value="false"/>
                                                <struct name="dmaLogicChannelConfig_TransferControlType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                                   <setting name="dmaLogicChannelConfig_enStart" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferSourceType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                                   <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                                </struct>
                                             </struct>
                                          </struct>
                                          <struct name="3">
                                             <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherArrayType_3"/>
                                             <struct name="dmaLogicChannelConfig_ScatterGatherElementConfigType">
                                                <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherElementConfigType"/>
                                                <setting name="dmaLogicChannelConfig_ScatterGatherElementNameType" value="DMA_LOGIC_CH_0_SGA_ELEMENT_3"/>
                                                <setting name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" value="false"/>
                                                <setting name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_4"/>
                                                <setting name="dmaLogicChannelConfig_enScatterGatherConfig" value="false"/>
                                                <struct name="dmaLogicChannelConfig_TransferControlType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                                   <setting name="dmaLogicChannelConfig_enStart" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferSourceType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                                   <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                                </struct>
                                             </struct>
                                          </struct>
                                          <struct name="4">
                                             <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherArrayType_4"/>
                                             <struct name="dmaLogicChannelConfig_ScatterGatherElementConfigType">
                                                <setting name="Name" value="dmaLogicChannelConfig_ScatterGatherElementConfigType"/>
                                                <setting name="dmaLogicChannelConfig_ScatterGatherElementNameType" value="DMA_LOGIC_CH_0_SGA_ELEMENT_4"/>
                                                <setting name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" value="true"/>
                                                <setting name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_0"/>
                                                <setting name="dmaLogicChannelConfig_enScatterGatherConfig" value="false"/>
                                                <struct name="dmaLogicChannelConfig_TransferControlType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferControlType"/>
                                                   <setting name="dmaLogicChannelConfig_enStart" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_disDmaAutoHwReq" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_bandwidthControl" value="DMA_IP_BWC_ENGINE_NO_STALL"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationStoreAddressType" value="0U"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferSourceType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferSourceType"/>
                                                   <setting name="dmaLogicChannelConfig_SourceSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_SourceModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferDestinationType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferDestinationType"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationSignedOffsetType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" value="0"/>
                                                   <setting name="dmaTransferConfig_TransferSizeType" value="DMA_IP_TRANSFER_SIZE_1_BYTE"/>
                                                   <setting name="dmaLogicChannelConfig_DestinationModuloType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMinorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMinorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enSourceOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_enDestinationOffset" value="false"/>
                                                   <setting name="dmaLogicChannelConfig_OffsetValueType" value="0"/>
                                                   <setting name="dmaLogicChannelConfig_enMinorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MinorLoopSizeType" value="0"/>
                                                </struct>
                                                <struct name="dmaLogicChannelConfig_TransferMajorLoopType">
                                                   <setting name="Name" value="dmaLogicChannelConfig_TransferMajorLoopType"/>
                                                   <setting name="dmaLogicChannelConfig_enMajorLoopLinkCh" value="false"/>
                                                   <setting name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" value="/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1"/>
                                                   <setting name="dmaLogicChannelConfig_MajorLoopCountType" value="1"/>
                                                </struct>
                                             </struct>
                                          </struct>
                                       </array>
                                    </struct>
                                    <struct name="dmaLogicChannel_CrcConfigType">
                                       <setting name="Name" value="dmaLogicChannel_CrcConfigType"/>
                                       <setting name="dmaLogicChannel_HwCrcChId" value="DMA_IP_HW_CRC_0_CH_0"/>
                                       <setting name="dmaLogicChannel_CrcMode" value="NORMAL_CRC_MODE"/>
                                       <setting name="dmaLogicChannel_CrcPolynomial" value="ETHERNET_CCITT32_CRC32"/>
                                       <setting name="dmaLogicChannel_CrcInitialValue" value="0"/>
                                       <setting name="dmaLogicChannel_CrcEnInitSel" value="false"/>
                                       <setting name="dmaLogicChannel_CrcEnLogic" value="true"/>
                                    </struct>
                                 </struct>
                              </struct>
                           </array>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Mcu" uuid="42a1bd28-83e2-4338-9b53-b7a47ce30d43" type="Mcu" type_id="Mcu" mode="autosar" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Mcu">
                        <setting name="Name" value="Mcu"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="McuGeneralConfiguration">
                           <setting name="Name" value="McuGeneralConfiguration"/>
                           <setting name="McuDevErrorDetect" value="false"/>
                           <setting name="McuVersionInfoApi" value="false"/>
                           <setting name="McuGetRamStateApi" value="false"/>
                           <setting name="McuInitClock" value="true"/>
                           <setting name="McuNoPll" value="false"/>
                           <setting name="McuEnterLowPowerMode" value="false"/>
                           <setting name="McuTimeout" value="50000"/>
                           <setting name="McuEnableUserModeSupport" value="false"/>
                           <setting name="McuPerformResetApi" value="false"/>
                           <setting name="McuCalloutBeforePerformReset" value="false"/>
                           <setting name="McuPerformResetCallout" value="NULL_PTR"/>
                           <array name="McuCmuNotification"/>
                           <setting name="McuAlternateResetIsrUsed" value="false"/>
                           <setting name="McuScmiPlatformSupport" value="true"/>
                           <setting name="McuCmuErrorIsrUsed" value="false"/>
                           <array name="McuErrorIsrNotification"/>
                           <setting name="McuDisableRgmInit" value="false"/>
                           <setting name="McuDisablePmcInit" value="false"/>
                           <setting name="McuDisableRamWaitStatesConfig" value="false"/>
                           <array name="McuPrepareMemoryConfig"/>
                           <setting name="McuTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="A53CoreFlavour" value="f1300MHz"/>
                           <array name="McuEcucPartitionRef"/>
                           <struct name="McuControlledClocksConfiguration">
                              <setting name="Name" value="McuControlledClocksConfiguration"/>
                              <setting name="McuFxoscUnderMcuControl" value="true"/>
                              <setting name="McuPll0UnderMcuControl" value="true"/>
                              <setting name="McuPll1UnderMcuControl" value="true"/>
                              <setting name="McuPll2UnderMcuControl" value="true"/>
                              <setting name="McuPll3UnderMcuControl" value="true"/>
                              <setting name="McuDfs0UnderMcuControl" value="true"/>
                              <setting name="McuDfs1UnderMcuControl" value="true"/>
                           </struct>
                        </struct>
                        <struct name="McuDebugConfiguration">
                           <setting name="Name" value="McuDebugConfiguration"/>
                           <setting name="McuDisableDemReportErrorStatus" value="true"/>
                           <setting name="McuGetSystemStateApi" value="false"/>
                           <setting name="McuGetPowerModeStateApi" value="false"/>
                           <setting name="McuGetPowerDomainApi" value="false"/>
                           <setting name="McuSscmGetMemConfigApi" value="false"/>
                           <setting name="McuSscmGetStatusApi" value="false"/>
                           <setting name="McuSscmGetUoptApi" value="false"/>
                           <setting name="McuGetMidrStructureApi" value="false"/>
                           <setting name="McuDisableCmuApi" value="false"/>
                           <setting name="McuEmiosConfigureGprenApi" value="false"/>
                           <setting name="McuGetClockFrequencyApi" value="false"/>
                        </struct>
                        <struct name="McuCoreControlConfiguration">
                           <setting name="Name" value="McuCoreControlConfiguration"/>
                           <setting name="McuCoreBootAddressControl" value="false"/>
                        </struct>
                        <struct name="McuPublishedInformation">
                           <setting name="Name" value="McuPublishedInformation"/>
                           <array name="McuResetReasonConf">
                              <struct name="0">
                                 <setting name="Name" value="MCU_POWER_ON_RESET"/>
                                 <setting name="McuResetReason" value="0"/>
                              </struct>
                              <struct name="1">
                                 <setting name="Name" value="MCU_NC_SPD_RST_RESET"/>
                                 <setting name="McuResetReason" value="1"/>
                              </struct>
                              <struct name="2">
                                 <setting name="Name" value="MCU_FCCU_FTR_RESET"/>
                                 <setting name="McuResetReason" value="2"/>
                              </struct>
                              <struct name="3">
                                 <setting name="Name" value="MCU_STCU_URF_RESET"/>
                                 <setting name="McuResetReason" value="3"/>
                              </struct>
                              <struct name="4">
                                 <setting name="Name" value="MCU_MC_RGM_FRE_RESET"/>
                                 <setting name="McuResetReason" value="4"/>
                              </struct>
                              <struct name="5">
                                 <setting name="Name" value="MCU_FXOSC_FAIL_RESET"/>
                                 <setting name="McuResetReason" value="5"/>
                              </struct>
                              <struct name="6">
                                 <setting name="Name" value="MCU_CORE_LOL_RESET"/>
                                 <setting name="McuResetReason" value="6"/>
                              </struct>
                              <struct name="7">
                                 <setting name="Name" value="MCU_PERIPH_LOL_RESET"/>
                                 <setting name="McuResetReason" value="7"/>
                              </struct>
                              <struct name="8">
                                 <setting name="Name" value="MCU_DDR_LOL_RESET"/>
                                 <setting name="McuResetReason" value="8"/>
                              </struct>
                              <struct name="9">
                                 <setting name="Name" value="MCU_ACC_LOL_RESET"/>
                                 <setting name="McuResetReason" value="9"/>
                              </struct>
                              <struct name="10">
                                 <setting name="Name" value="MCU_XBAR_DIV3_CLK_FAIL_RESET"/>
                                 <setting name="McuResetReason" value="10"/>
                              </struct>
                              <struct name="11">
                                 <setting name="Name" value="MCU_HSE_LC_RST_RESET"/>
                                 <setting name="McuResetReason" value="11"/>
                              </struct>
                              <struct name="12">
                                 <setting name="Name" value="MCU_HSE_SNVS_RST_RESET"/>
                                 <setting name="McuResetReason" value="12"/>
                              </struct>
                              <struct name="13">
                                 <setting name="Name" value="MCU_HSE_SWT_RST_RESET"/>
                                 <setting name="McuResetReason" value="13"/>
                              </struct>
                              <struct name="14">
                                 <setting name="Name" value="MCU_SW_DEST_RESET"/>
                                 <setting name="McuResetReason" value="14"/>
                              </struct>
                              <struct name="15">
                                 <setting name="Name" value="MCU_DEBUG_DEST_RESET"/>
                                 <setting name="McuResetReason" value="15"/>
                              </struct>
                              <struct name="16">
                                 <setting name="Name" value="MCU_EXT_RESET"/>
                                 <setting name="McuResetReason" value="16"/>
                              </struct>
                              <struct name="17">
                                 <setting name="Name" value="MCU_FCCU_RST_RESET"/>
                                 <setting name="McuResetReason" value="17"/>
                              </struct>
                              <struct name="18">
                                 <setting name="Name" value="MCU_ST_DONE_RESET"/>
                                 <setting name="McuResetReason" value="18"/>
                              </struct>
                              <struct name="19">
                                 <setting name="Name" value="MCU_SWT0_RST_RESET"/>
                                 <setting name="McuResetReason" value="19"/>
                              </struct>
                              <struct name="20">
                                 <setting name="Name" value="MCU_HSE_RAM_ECC_RST_RESET"/>
                                 <setting name="McuResetReason" value="20"/>
                              </struct>
                              <struct name="21">
                                 <setting name="Name" value="MCU_HSE_BOOT_ERR_RST_RESET"/>
                                 <setting name="McuResetReason" value="21"/>
                              </struct>
                              <struct name="22">
                                 <setting name="Name" value="MCU_HSE_CORE_LOCK_RST_RESET"/>
                                 <setting name="McuResetReason" value="22"/>
                              </struct>
                              <struct name="23">
                                 <setting name="Name" value="MCU_SW_FUNC_RESET"/>
                                 <setting name="McuResetReason" value="23"/>
                              </struct>
                              <struct name="24">
                                 <setting name="Name" value="MCU_DEBUG_FUNC_RESET"/>
                                 <setting name="McuResetReason" value="24"/>
                              </struct>
                              <struct name="25">
                                 <setting name="Name" value="MCU_WAKEUP_REASON"/>
                                 <setting name="McuResetReason" value="25"/>
                              </struct>
                              <struct name="26">
                                 <setting name="Name" value="MCU_NO_RESET_REASON"/>
                                 <setting name="McuResetReason" value="26"/>
                              </struct>
                              <struct name="27">
                                 <setting name="Name" value="MCU_MULTIPLE_RESET_REASON"/>
                                 <setting name="McuResetReason" value="27"/>
                              </struct>
                              <struct name="28">
                                 <setting name="Name" value="MCU_RESET_UNDEFINED"/>
                                 <setting name="McuResetReason" value="28"/>
                              </struct>
                           </array>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                        </struct>
                        <struct name="McuModuleConfiguration">
                           <setting name="Name" value="McuModuleConfiguration"/>
                           <array name="McuResetSetting"/>
                           <setting name="McuClockSrcFailureNotification" value="DISABLED"/>
                           <array name="McuClockSettingConfig">
                              <struct name="0">
                                 <setting name="Name" value="McuClockSettingConfig_0"/>
                                 <setting name="Configuration" value="BOARD_BootClockRUN"/>
                                 <struct name="McuFXOSC">
                                    <setting name="Name" value="McuFXOSC"/>
                                    <setting name="McuFxoscUnderMcuControl" value="true"/>
                                    <setting name="McuFxoscPowerDownCtr" value="true"/>
                                    <setting name="McuFxoscByPass" value="false"/>
                                    <setting name="McuFxoscMainComparator" value="true"/>
                                    <setting name="McuFxoscALCEnable" value="Enabled"/>
                                 </struct>
                                 <struct name="McuCgm0SettingConfig">
                                    <setting name="Name" value="McuCgm0SettingConfig"/>
                                    <array name="McuCgm0PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_12"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm0ClockMux0">
                                       <setting name="Name" value="McuCgm0ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                       <setting name="McuClkMux0Div1_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux1">
                                       <setting name="Name" value="McuCgm0ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FXOSC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux2">
                                       <setting name="Name" value="McuCgm0ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FXOSC_CLK"/>
                                       <setting name="McuClkMux2Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux3">
                                       <setting name="Name" value="McuCgm0ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux3Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux4">
                                       <setting name="Name" value="McuCgm0ClockMux4"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux4_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux4Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux5">
                                       <setting name="Name" value="McuCgm0ClockMux5"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux5_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux5Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux6">
                                       <setting name="Name" value="McuCgm0ClockMux6"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux6_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux6Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux7">
                                       <setting name="Name" value="McuCgm0ClockMux7"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux7_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux8">
                                       <setting name="Name" value="McuCgm0ClockMux8"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux8_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux12">
                                       <setting name="Name" value="McuCgm0ClockMux12"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux12_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux12Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux14">
                                       <setting name="Name" value="McuCgm0ClockMux14"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux14_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux14Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm0ClockMux16">
                                       <setting name="Name" value="McuCgm0ClockMux16"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux16_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm1SettingConfig">
                                    <setting name="Name" value="McuCgm1SettingConfig"/>
                                    <array name="McuCgm1PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_4"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm1ClockMux0">
                                       <setting name="Name" value="McuCgm1ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm2SettingConfig">
                                    <setting name="Name" value="McuCgm2SettingConfig"/>
                                    <array name="McuCgm2PcsConfig">
                                       <struct name="0">
                                          <setting name="Name" value="McuCgmNamePcsConfig_0"/>
                                          <setting name="McuClockPcfsUnderMcuControl" value="false"/>
                                          <setting name="McuPCS_Name" value="PCFS_33"/>
                                       </struct>
                                    </array>
                                    <struct name="McuCgm2ClockMux0">
                                       <setting name="Name" value="McuCgm2ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                       <setting name="McuClkMux0Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux1">
                                       <setting name="Name" value="McuCgm2ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                       <setting name="McuClkMux1Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC0" quick_selection="Default">
                                       <setting name="Name" value="McuGENCTRL1_EMAC0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC0_Source" value="PFEMAC0_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC1">
                                       <setting name="Name" value="McuGENCTRL1_EMAC1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC1_Source" value="PFEMAC1_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuGENCTRL1_EMAC2">
                                       <setting name="Name" value="McuGENCTRL1_EMAC2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuGENCTRL1_EMAC2_Source" value="PFEMAC2_TX_DIV_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux2">
                                       <setting name="Name" value="McuCgm2ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux2Div0_En" value="true"/>
                                       <setting name="McuClkMux2Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux3">
                                       <setting name="Name" value="McuCgm2ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux3Div0_En" value="true"/>
                                       <setting name="McuClkMux3Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux4">
                                       <setting name="Name" value="McuCgm2ClockMux4"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux4_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux5">
                                       <setting name="Name" value="McuCgm2ClockMux5"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux5_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux6">
                                       <setting name="Name" value="McuCgm2ClockMux6"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux6_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux7">
                                       <setting name="Name" value="McuCgm2ClockMux7"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux7_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux7Div0_En" value="true"/>
                                       <setting name="McuClkMux7Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux8">
                                       <setting name="Name" value="McuCgm2ClockMux8"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux8_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux8Div0_En" value="true"/>
                                       <setting name="McuClkMux8Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                    <struct name="McuCgm2ClockMux9">
                                       <setting name="Name" value="McuCgm2ClockMux9"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux9_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux9Div0_En" value="true"/>
                                       <setting name="McuClkMux9Div0Trigger" value="COMMON_TRIGGER_DIVIDER_UPDATE"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm5SettingConfig">
                                    <setting name="Name" value="McuCgm5SettingConfig"/>
                                    <struct name="McuCgm5ClockMux0">
                                       <setting name="Name" value="McuCgm5ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCgm6SettingConfig">
                                    <setting name="Name" value="McuCgm6SettingConfig"/>
                                    <struct name="McuCgm6ClockMux0">
                                       <setting name="Name" value="McuCgm6ClockMux0"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux0_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux0Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux1">
                                       <setting name="Name" value="McuCgm6ClockMux1"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux1_Source" value="FIRC_CLK"/>
                                       <setting name="McuClkMux1Div0_En" value="true"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux2">
                                       <setting name="Name" value="McuCgm6ClockMux2"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux2_Source" value="FIRC_CLK"/>
                                    </struct>
                                    <struct name="McuCgm6ClockMux3">
                                       <setting name="Name" value="McuCgm6ClockMux3"/>
                                       <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                       <setting name="McuClkMux3_Source" value="FIRC_CLK"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuRtcClockSelect">
                                    <setting name="Name" value="McuRtcClockSelect"/>
                                    <setting name="McuClockMuxUnderMcuControl" value="true"/>
                                    <setting name="McuRtc_Source" value="FIRC_CLK"/>
                                 </struct>
                                 <struct name="McuPll_0">
                                    <setting name="Name" value="McuPll_0"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuCoreDfs">
                                    <setting name="Name" value="McuCoreDfs"/>
                                    <struct name="McuDfs_1">
                                       <setting name="Name" value="McuDfs_1"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_2">
                                       <setting name="Name" value="McuDfs_2"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_3">
                                       <setting name="Name" value="McuDfs_3"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_4">
                                       <setting name="Name" value="McuDfs_4"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_5">
                                       <setting name="Name" value="McuDfs_5"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_6">
                                       <setting name="Name" value="McuDfs_6"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_1">
                                    <setting name="Name" value="McuPll_1"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                       <setting name="McuPllOdiv2_En" value="true"/>
                                       <setting name="McuPllOdiv3_En" value="true"/>
                                       <setting name="McuPllOdiv4_En" value="true"/>
                                       <setting name="McuPllOdiv5_En" value="true"/>
                                       <setting name="McuPllOdiv6_En" value="true"/>
                                       <setting name="McuPllOdiv7_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPeriphDfs">
                                    <setting name="Name" value="McuPeriphDfs"/>
                                    <struct name="McuDfs_1">
                                       <setting name="Name" value="McuDfs_1"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_2">
                                       <setting name="Name" value="McuDfs_2"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_3">
                                       <setting name="Name" value="McuDfs_3"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_4">
                                       <setting name="Name" value="McuDfs_4"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                    <struct name="McuDfs_5">
                                       <setting name="Name" value="McuDfs_5"/>
                                       <setting name="McuDFSUnderMcuControl" value="true"/>
                                       <setting name="McuDFSPort_En" value="true"/>
                                    </struct>
                                    <struct name="McuDfs_6">
                                       <setting name="Name" value="McuDfs_6"/>
                                       <setting name="McuDFSUnderMcuControl" value="false"/>
                                       <setting name="McuDFSPort_En" value="false"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_2">
                                    <setting name="Name" value="McuPll_2"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                       <setting name="McuPllOdiv1_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <struct name="McuPll_3">
                                    <setting name="Name" value="McuPll_3"/>
                                    <setting name="McuPLLUnderMcuControl" value="true"/>
                                    <setting name="McuPLLEnabled" value="true"/>
                                    <setting name="McuPllClockSelection" value="FXOSC_CLK"/>
                                    <struct name="McuPll_Configuration">
                                       <setting name="Name" value="McuPll_Configuration"/>
                                       <setting name="McuPllFmSscgbyp" value="true"/>
                                       <setting name="McuPllFmSpreadctl" value="Center_Spread"/>
                                       <setting name="McuPllFdSdmen" value="false"/>
                                       <setting name="McuPllOdiv0_En" value="true"/>
                                    </struct>
                                    <struct name="McuPll_Parameter">
                                       <setting name="Name" value="McuPll_Parameter"/>
                                    </struct>
                                 </struct>
                                 <array name="McuClkMonitor">
                                    <struct name="0">
                                       <setting name="Name" value="McuClkMonitor_0"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_0_FXOSC_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="1">
                                       <setting name="Name" value="McuClkMonitor_1"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_5_XBAR_DIV3_FAIL_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="2">
                                       <setting name="Name" value="McuClkMonitor_2"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_6_CORE_M7_0_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="3">
                                       <setting name="Name" value="McuClkMonitor_3"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_7_XBAR_DIV3_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="4">
                                       <setting name="Name" value="McuClkMonitor_4"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_8_CORE_M7_1_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="5">
                                       <setting name="Name" value="McuClkMonitor_5"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_9_CORE_M7_2_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="6">
                                       <setting name="Name" value="McuClkMonitor_6"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_10_PER_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="7">
                                       <setting name="Name" value="McuClkMonitor_7"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_11_SERDES_REF_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="8">
                                       <setting name="Name" value="McuClkMonitor_8"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_12_FLEXRAY_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="9">
                                       <setting name="Name" value="McuClkMonitor_9"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_13_FLEXCAN_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="10">
                                       <setting name="Name" value="McuClkMonitor_10"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_14_GMAC0_TX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="11">
                                       <setting name="Name" value="McuClkMonitor_11"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_15_GMAC_TS_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="12">
                                       <setting name="Name" value="McuClkMonitor_12"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_16_LINFLEXD_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="13">
                                       <setting name="Name" value="McuClkMonitor_13"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_17_QSPI_1X_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="14">
                                       <setting name="Name" value="McuClkMonitor_14"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_18_SDHC_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="15">
                                       <setting name="Name" value="McuClkMonitor_15"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_20_DDR_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="16">
                                       <setting name="Name" value="McuClkMonitor_16"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_21_GMAC0_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="17">
                                       <setting name="Name" value="McuClkMonitor_17"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_22_SPI_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="18">
                                       <setting name="Name" value="McuClkMonitor_18"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_24_CORE_M7_3_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="19">
                                       <setting name="Name" value="McuClkMonitor_19"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="false"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_27_CORE_A53_CLUSTER_0_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="20">
                                       <setting name="Name" value="McuClkMonitor_20"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="false"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_28_CORE_A53_CLUSTER_1_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="21">
                                       <setting name="Name" value="McuClkMonitor_21"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_39_PFE_SYS_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="22">
                                       <setting name="Name" value="McuClkMonitor_22"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_46_PFEMAC0_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="23">
                                       <setting name="Name" value="McuClkMonitor_23"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_47_PFEMAC0_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="24">
                                       <setting name="Name" value="McuClkMonitor_24"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_48_PFEMAC1_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="25">
                                       <setting name="Name" value="McuClkMonitor_25"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_49_PFEMAC1_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="26">
                                       <setting name="Name" value="McuClkMonitor_26"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_50_PFEMAC2_TX_DIV_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                    <struct name="27">
                                       <setting name="Name" value="McuClkMonitor_27"/>
                                       <setting name="McuClockMonitorUnderMcuControl" value="true"/>
                                       <setting name="McuClkMonitorEn" value="false"/>
                                       <setting name="McuCmuName" value="CMU_FC_51_PFEMAC2_RX_CLK"/>
                                       <setting name="McuAsyncFHHInterruptEn" value="false"/>
                                       <setting name="McuAsyncFLLInterruptEn" value="false"/>
                                       <setting name="McuSyncFHHInterruptEn" value="false"/>
                                       <setting name="McuSyncFLLInterruptEn" value="false"/>
                                    </struct>
                                 </array>
                                 <array name="McuClockReferencePoint">
                                    <struct name="0">
                                       <setting name="Name" value="McuClockReferencePoint_0"/>
                                       <setting name="McuClockFrequencySelect" value="XBAR_CLK"/>
                                    </struct>
                                 </array>
                              </struct>
                           </array>
                           <array name="McuDemEventParameterRefs"/>
                           <array name="McuModeSettingConf">
                              <struct name="0">
                                 <setting name="Name" value="McuModeSettingConf_0"/>
                                 <setting name="McuMode" value="0"/>
                                 <setting name="McuPowerMode" value="RUN"/>
                                 <setting name="McuMainCoreSelect" value="HSE_CM7"/>
                                 <setting name="McuEnableSleepOnExit" value="false"/>
                                 <struct name="McuPartitionConfiguration">
                                    <setting name="Name" value="McuPartitionConfiguration"/>
                                    <struct name="McuPartition0Config">
                                       <setting name="Name" value="McuPartition0Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="true"/>
                                       <setting name="McuPartitionResetEnable" value="false"/>
                                       <struct name="McuCore0Configuration">
                                          <setting name="Name" value="McuCore0Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="true"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore1Configuration">
                                          <setting name="Name" value="McuCore1Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore2Configuration">
                                          <setting name="Name" value="McuCore2Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore4Configuration">
                                          <setting name="Name" value="McuCore4Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="false"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="false"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                    </struct>
                                    <struct name="McuPartition1Config">
                                       <setting name="Name" value="McuPartition1Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                       <struct name="McuCore0Configuration">
                                          <setting name="Name" value="McuCore0Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore1Configuration">
                                          <setting name="Name" value="McuCore1Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore2Configuration">
                                          <setting name="Name" value="McuCore2Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore3Configuration">
                                          <setting name="Name" value="McuCore3Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore4Configuration">
                                          <setting name="Name" value="McuCore4Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore5Configuration">
                                          <setting name="Name" value="McuCore5Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore6Configuration">
                                          <setting name="Name" value="McuCore6Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                       <struct name="McuCore7Configuration">
                                          <setting name="Name" value="McuCore7Configuration"/>
                                          <setting name="McuCoreUnderMcuControl" value="true"/>
                                          <setting name="McuCoreClockEnable" value="false"/>
                                          <setting name="McuCoreResetEnable" value="true"/>
                                          <setting name="McuCoreBootAddress" value="0"/>
                                          <setting name="McuCoreBootAddressLinkerSym" value=""/>
                                       </struct>
                                    </struct>
                                    <struct name="McuPartition2Config">
                                       <setting name="Name" value="McuPartition2Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                    </struct>
                                    <struct name="McuPartition3Config">
                                       <setting name="Name" value="McuPartition3Config"/>
                                       <setting name="McuPartitionUnderMcuControl" value="true"/>
                                       <setting name="McuPartitionPowerUnderMcuControl" value="true"/>
                                       <setting name="McuPrtnCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPrstCofb0UnderMcuControl" value="true"/>
                                       <setting name="McuPartitionClockEnable" value="false"/>
                                       <setting name="McuPartitionResetEnable" value="true"/>
                                    </struct>
                                    <array name="McuPeripheral">
                                       <struct name="0">
                                          <setting name="Name" value="McuPeripheral_0"/>
                                          <setting name="McuPeripheralName" value="uSDHC"/>
                                          <setting name="McuModeEntrySlot" value="PRTN0_COFB0_REQ0"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="1">
                                          <setting name="Name" value="McuPeripheral_1"/>
                                          <setting name="McuPeripheralName" value="DDR_0"/>
                                          <setting name="McuModeEntrySlot" value="PRTN0_COFB0_REQ1"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_3"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="2">
                                          <setting name="Name" value="McuPeripheral_2"/>
                                          <setting name="McuPeripheralName" value="PCIe_0"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_4"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="3">
                                          <setting name="Name" value="McuPeripheral_3"/>
                                          <setting name="McuPeripheralName" value="PCIe_0_CSS"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_5"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="4">
                                          <setting name="Name" value="McuPeripheral_4"/>
                                          <setting name="McuPeripheralName" value="PCIe_1"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_16"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="5">
                                          <setting name="Name" value="McuPeripheral_5"/>
                                          <setting name="McuPeripheralName" value="PCIe_1_CSS"/>
                                          <setting name="McuModeEntrySlot" value="NONE"/>
                                          <setting name="McuResetGenerationSlot" value="PRST0_COFB0_PERIPH_17"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="true"/>
                                       </struct>
                                       <struct name="6">
                                          <setting name="Name" value="McuPeripheral_6"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC0"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ0"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="7">
                                          <setting name="Name" value="McuPeripheral_7"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC1"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ1"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="8">
                                          <setting name="Name" value="McuPeripheral_8"/>
                                          <setting name="McuPeripheralName" value="PFE_MAC2"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ2"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                       <struct name="9">
                                          <setting name="Name" value="McuPeripheral_9"/>
                                          <setting name="McuPeripheralName" value="PFE_TS_CLK"/>
                                          <setting name="McuModeEntrySlot" value="PRTN2_COFB0_REQ3"/>
                                          <setting name="McuResetGenerationSlot" value="NONE"/>
                                          <setting name="McuPeripheralClockEnable" value="false"/>
                                          <setting name="McuPeripheralResetEnable" value="false"/>
                                       </struct>
                                    </array>
                                 </struct>
                              </struct>
                           </array>
                           <array name="McuRamSectorSettingConf"/>
                           <struct name="McuResetConfig">
                              <setting name="Name" value="McuResetConfig"/>
                              <setting name="McuResetType" value="FunctionalReset"/>
                              <setting name="McuFuncResetEscThreshold" value="15"/>
                              <setting name="McuDestResetEscThreshold" value="0"/>
                              <struct name="McuResetSourcesConfig">
                                 <setting name="Name" value="McuResetSourcesConfig"/>
                                 <struct name="McuEXR_ResetSource">
                                    <setting name="Name" value="McuEXR_ResetSource"/>
                                    <setting name="McuDisableReset" value="false"/>
                                 </struct>
                                 <struct name="McuF_FR_31_ResetSource">
                                    <setting name="Name" value="McuF_FR_31_ResetSource"/>
                                    <setting name="McuDisableReset" value="false"/>
                                 </struct>
                              </struct>
                           </struct>
                           <struct name="McuPowerControl">
                              <setting name="Name" value="McuPowerControl"/>
                              <struct name="McuPMC_Config">
                                 <setting name="Name" value="McuPMC_Config"/>
                                 <setting name="McuVDD_FXOSCNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_ADC0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_ADC1NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_TMUNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_EFUSENonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_HV_PLL_DDR0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PLL_DDR0NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_HV_PLL_AURNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PLL_AURNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_STBYNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_ANonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_BNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_USBNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_SDHCNonCriticalFlag" value="false"/>
                                 <setting name="McuPADS_CLKOUTNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_IO_QSPINonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_PERIPH_PLLNonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_ACC_PLL_30NonCriticalFlag" value="false"/>
                                 <setting name="McuVDD_LV_ACC_PLL_31NonCriticalFlag" value="false"/>
                              </struct>
                           </struct>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Platform" uuid="ef4b3283-534d-4756-af8a-b34be17ab200" type="Platform" type_id="Platform" mode="autosar" enabled="true" comment="" custom_name_enabled="true" editing_lock="false">
                     <config_set name="Platform">
                        <setting name="Name" value="Platform"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="GeneralConfiguration">
                           <setting name="Name" value="GeneralConfiguration"/>
                           <setting name="PlatformDevErrorDetect" value="true"/>
                           <setting name="PlatformMcmConfigurable" value="false"/>
                           <setting name="PlatformEnableIntCtrlConfiguration" value="true"/>
                           <setting name="PlatformEnableMSIConfiguration" value="false"/>
                           <setting name="PlatformIpAPIsAvailable" value="false"/>
                           <setting name="PlatformEnableUserModeSupport" value="false"/>
                           <setting name="PlatformMulticoreSupport" value="false"/>
                           <array name="PlatformEcucPartitionRef"/>
                        </struct>
                        <struct name="CommonPublishedInformation">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="ModuleId" value="255"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                           <array name="VendorApiInfix"/>
                           <setting name="VendorId" value="43"/>
                        </struct>
                        <array name="McmConfig"/>
                        <array name="IntCtrlConfig">
                           <struct name="0">
                              <setting name="Name" value="IntCtrlConfig_0"/>
                              <array name="PlatformIsrConfig">
                                 <struct name="0">
                                    <setting name="Name" value="PlatformIsrConfig_0"/>
                                    <setting name="IsrName" value="MSCM_Pcie_1_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="1">
                                    <setting name="Name" value="PlatformIsrConfig_1"/>
                                    <setting name="IsrName" value="MSCM_INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="2">
                                    <setting name="Name" value="PlatformIsrConfig_2"/>
                                    <setting name="IsrName" value="MSCM_INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="3">
                                    <setting name="Name" value="PlatformIsrConfig_3"/>
                                    <setting name="IsrName" value="MSCM_INT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="4">
                                    <setting name="Name" value="PlatformIsrConfig_4"/>
                                    <setting name="IsrName" value="MSCM_Pcie_0_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="5">
                                    <setting name="Name" value="PlatformIsrConfig_5"/>
                                    <setting name="IsrName" value="CTI_INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="6">
                                    <setting name="Name" value="PlatformIsrConfig_6"/>
                                    <setting name="IsrName" value="CTI_INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="7">
                                    <setting name="Name" value="PlatformIsrConfig_7"/>
                                    <setting name="IsrName" value="MCM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="8">
                                    <setting name="Name" value="PlatformIsrConfig_8"/>
                                    <setting name="IsrName" value="DMA0_0_15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="9">
                                    <setting name="Name" value="PlatformIsrConfig_9"/>
                                    <setting name="IsrName" value="DMA0_16_31_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="10">
                                    <setting name="Name" value="PlatformIsrConfig_10"/>
                                    <setting name="IsrName" value="DMA0_ERR0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="11">
                                    <setting name="Name" value="PlatformIsrConfig_11"/>
                                    <setting name="IsrName" value="DMA1_0_15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="12">
                                    <setting name="Name" value="PlatformIsrConfig_12"/>
                                    <setting name="IsrName" value="DMA1_16_31_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="13">
                                    <setting name="Name" value="PlatformIsrConfig_13"/>
                                    <setting name="IsrName" value="DMA1_ERR0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="14">
                                    <setting name="Name" value="PlatformIsrConfig_14"/>
                                    <setting name="IsrName" value="SWT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="15">
                                    <setting name="Name" value="PlatformIsrConfig_15"/>
                                    <setting name="IsrName" value="SWT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="16">
                                    <setting name="Name" value="PlatformIsrConfig_16"/>
                                    <setting name="IsrName" value="SWT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="17">
                                    <setting name="Name" value="PlatformIsrConfig_17"/>
                                    <setting name="IsrName" value="SWT3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="18">
                                    <setting name="Name" value="PlatformIsrConfig_18"/>
                                    <setting name="IsrName" value="SWT4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="19">
                                    <setting name="Name" value="PlatformIsrConfig_19"/>
                                    <setting name="IsrName" value="SWT5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="20">
                                    <setting name="Name" value="PlatformIsrConfig_20"/>
                                    <setting name="IsrName" value="SWT6_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="21">
                                    <setting name="Name" value="PlatformIsrConfig_21"/>
                                    <setting name="IsrName" value="SWT7_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="22">
                                    <setting name="Name" value="PlatformIsrConfig_22"/>
                                    <setting name="IsrName" value="MSCM_INT3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="23">
                                    <setting name="Name" value="PlatformIsrConfig_23"/>
                                    <setting name="IsrName" value="MSCM_INT4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="24">
                                    <setting name="Name" value="PlatformIsrConfig_24"/>
                                    <setting name="IsrName" value="STM0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="25">
                                    <setting name="Name" value="PlatformIsrConfig_25"/>
                                    <setting name="IsrName" value="STM1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="26">
                                    <setting name="Name" value="PlatformIsrConfig_26"/>
                                    <setting name="IsrName" value="STM2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="27">
                                    <setting name="Name" value="PlatformIsrConfig_27"/>
                                    <setting name="IsrName" value="STM3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="28">
                                    <setting name="Name" value="PlatformIsrConfig_28"/>
                                    <setting name="IsrName" value="STM4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="29">
                                    <setting name="Name" value="PlatformIsrConfig_29"/>
                                    <setting name="IsrName" value="STM5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="30">
                                    <setting name="Name" value="PlatformIsrConfig_30"/>
                                    <setting name="IsrName" value="STM6_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="31">
                                    <setting name="Name" value="PlatformIsrConfig_31"/>
                                    <setting name="IsrName" value="STM7_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="32">
                                    <setting name="Name" value="PlatformIsrConfig_32"/>
                                    <setting name="IsrName" value="QSPI0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="33">
                                    <setting name="Name" value="PlatformIsrConfig_33"/>
                                    <setting name="IsrName" value="QSPI1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="34">
                                    <setting name="Name" value="PlatformIsrConfig_34"/>
                                    <setting name="IsrName" value="QSPI2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="35">
                                    <setting name="Name" value="PlatformIsrConfig_35"/>
                                    <setting name="IsrName" value="STCU2_LBIST_MBIST_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="36">
                                    <setting name="Name" value="PlatformIsrConfig_36"/>
                                    <setting name="IsrName" value="USDHC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="37">
                                    <setting name="Name" value="PlatformIsrConfig_37"/>
                                    <setting name="IsrName" value="CAN0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="38">
                                    <setting name="Name" value="PlatformIsrConfig_38"/>
                                    <setting name="IsrName" value="CAN0_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="39">
                                    <setting name="Name" value="PlatformIsrConfig_39"/>
                                    <setting name="IsrName" value="CAN0_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="40">
                                    <setting name="Name" value="PlatformIsrConfig_40"/>
                                    <setting name="IsrName" value="CAN0_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="41">
                                    <setting name="Name" value="PlatformIsrConfig_41"/>
                                    <setting name="IsrName" value="CAN1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="42">
                                    <setting name="Name" value="PlatformIsrConfig_42"/>
                                    <setting name="IsrName" value="CAN1_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="43">
                                    <setting name="Name" value="PlatformIsrConfig_43"/>
                                    <setting name="IsrName" value="CAN1_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="44">
                                    <setting name="Name" value="PlatformIsrConfig_44"/>
                                    <setting name="IsrName" value="CAN1_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="45">
                                    <setting name="Name" value="PlatformIsrConfig_45"/>
                                    <setting name="IsrName" value="CAN2_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="46">
                                    <setting name="Name" value="PlatformIsrConfig_46"/>
                                    <setting name="IsrName" value="CAN2_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="47">
                                    <setting name="Name" value="PlatformIsrConfig_47"/>
                                    <setting name="IsrName" value="CAN2_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="48">
                                    <setting name="Name" value="PlatformIsrConfig_48"/>
                                    <setting name="IsrName" value="CAN2_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="49">
                                    <setting name="Name" value="PlatformIsrConfig_49"/>
                                    <setting name="IsrName" value="CAN3_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="50">
                                    <setting name="Name" value="PlatformIsrConfig_50"/>
                                    <setting name="IsrName" value="CAN3_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="51">
                                    <setting name="Name" value="PlatformIsrConfig_51"/>
                                    <setting name="IsrName" value="CAN3_ORED_0_7_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="52">
                                    <setting name="Name" value="PlatformIsrConfig_52"/>
                                    <setting name="IsrName" value="CAN3_ORED_8_127_MB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="53">
                                    <setting name="Name" value="PlatformIsrConfig_53"/>
                                    <setting name="IsrName" value="PIT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="54">
                                    <setting name="Name" value="PlatformIsrConfig_54"/>
                                    <setting name="IsrName" value="PIT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="55">
                                    <setting name="Name" value="PlatformIsrConfig_55"/>
                                    <setting name="IsrName" value="FTM0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="56">
                                    <setting name="Name" value="PlatformIsrConfig_56"/>
                                    <setting name="IsrName" value="FTM1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="57">
                                    <setting name="Name" value="PlatformIsrConfig_57"/>
                                    <setting name="IsrName" value="GMAC0_Common_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="58">
                                    <setting name="Name" value="PlatformIsrConfig_58"/>
                                    <setting name="IsrName" value="GMAC0_CH0_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="59">
                                    <setting name="Name" value="PlatformIsrConfig_59"/>
                                    <setting name="IsrName" value="GMAC0_CH0_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="60">
                                    <setting name="Name" value="PlatformIsrConfig_60"/>
                                    <setting name="IsrName" value="GMAC0_CH1_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="61">
                                    <setting name="Name" value="PlatformIsrConfig_61"/>
                                    <setting name="IsrName" value="GMAC0_CH1_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="62">
                                    <setting name="Name" value="PlatformIsrConfig_62"/>
                                    <setting name="IsrName" value="GMAC0_CH2_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="63">
                                    <setting name="Name" value="PlatformIsrConfig_63"/>
                                    <setting name="IsrName" value="GMAC0_CH2_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="64">
                                    <setting name="Name" value="PlatformIsrConfig_64"/>
                                    <setting name="IsrName" value="GMAC0_CH3_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="65">
                                    <setting name="Name" value="PlatformIsrConfig_65"/>
                                    <setting name="IsrName" value="GMAC0_CH3_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="66">
                                    <setting name="Name" value="PlatformIsrConfig_66"/>
                                    <setting name="IsrName" value="GMAC0_CH4_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="67">
                                    <setting name="Name" value="PlatformIsrConfig_67"/>
                                    <setting name="IsrName" value="GMAC0_CH4_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="68">
                                    <setting name="Name" value="PlatformIsrConfig_68"/>
                                    <setting name="IsrName" value="MSCM_INT5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="69">
                                    <setting name="Name" value="PlatformIsrConfig_69"/>
                                    <setting name="IsrName" value="MSCM_INT6_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="70">
                                    <setting name="Name" value="PlatformIsrConfig_70"/>
                                    <setting name="IsrName" value="SAR_ADC0_INT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="71">
                                    <setting name="Name" value="PlatformIsrConfig_71"/>
                                    <setting name="IsrName" value="SAR_ADC1_INT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="72">
                                    <setting name="Name" value="PlatformIsrConfig_72"/>
                                    <setting name="IsrName" value="FLEXRAY0_NCERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="73">
                                    <setting name="Name" value="PlatformIsrConfig_73"/>
                                    <setting name="IsrName" value="FLEXRAY0_CERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="74">
                                    <setting name="Name" value="PlatformIsrConfig_74"/>
                                    <setting name="IsrName" value="FLEXRAY0_CH0_RX_FIFO_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="75">
                                    <setting name="Name" value="PlatformIsrConfig_75"/>
                                    <setting name="IsrName" value="FLEXRAY0_CH1_RX_FIFO_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="76">
                                    <setting name="Name" value="PlatformIsrConfig_76"/>
                                    <setting name="IsrName" value="FLEXRAY0_WKUP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="77">
                                    <setting name="Name" value="PlatformIsrConfig_77"/>
                                    <setting name="IsrName" value="FLEXRAY0_STATUS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="78">
                                    <setting name="Name" value="PlatformIsrConfig_78"/>
                                    <setting name="IsrName" value="FLEXRAY0_CMBERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="79">
                                    <setting name="Name" value="PlatformIsrConfig_79"/>
                                    <setting name="IsrName" value="FLEXRAY0_TX_BUFF_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="80">
                                    <setting name="Name" value="PlatformIsrConfig_80"/>
                                    <setting name="IsrName" value="FLEXRAY0_RX_BUFF_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="81">
                                    <setting name="Name" value="PlatformIsrConfig_81"/>
                                    <setting name="IsrName" value="FLEXRAY0_MODULE_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="82">
                                    <setting name="Name" value="PlatformIsrConfig_82"/>
                                    <setting name="IsrName" value="LINFLEXD0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="83">
                                    <setting name="Name" value="PlatformIsrConfig_83"/>
                                    <setting name="IsrName" value="LINFLEXD1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="84">
                                    <setting name="Name" value="PlatformIsrConfig_84"/>
                                    <setting name="IsrName" value="LINFLEXD2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="85">
                                    <setting name="Name" value="PlatformIsrConfig_85"/>
                                    <setting name="IsrName" value="SPI0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="86">
                                    <setting name="Name" value="PlatformIsrConfig_86"/>
                                    <setting name="IsrName" value="SPI1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="87">
                                    <setting name="Name" value="PlatformIsrConfig_87"/>
                                    <setting name="IsrName" value="SPI2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="88">
                                    <setting name="Name" value="PlatformIsrConfig_88"/>
                                    <setting name="IsrName" value="SPI3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="89">
                                    <setting name="Name" value="PlatformIsrConfig_89"/>
                                    <setting name="IsrName" value="SPI4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="90">
                                    <setting name="Name" value="PlatformIsrConfig_90"/>
                                    <setting name="IsrName" value="SPI5_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="91">
                                    <setting name="Name" value="PlatformIsrConfig_91"/>
                                    <setting name="IsrName" value="I2C0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="92">
                                    <setting name="Name" value="PlatformIsrConfig_92"/>
                                    <setting name="IsrName" value="I2C1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="93">
                                    <setting name="Name" value="PlatformIsrConfig_93"/>
                                    <setting name="IsrName" value="I2C2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="94">
                                    <setting name="Name" value="PlatformIsrConfig_94"/>
                                    <setting name="IsrName" value="I2C3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="95">
                                    <setting name="Name" value="PlatformIsrConfig_95"/>
                                    <setting name="IsrName" value="I2C4_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="96">
                                    <setting name="Name" value="PlatformIsrConfig_96"/>
                                    <setting name="IsrName" value="MC_RGM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="97">
                                    <setting name="Name" value="PlatformIsrConfig_97"/>
                                    <setting name="IsrName" value="FCCU_ALARM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="98">
                                    <setting name="Name" value="PlatformIsrConfig_98"/>
                                    <setting name="IsrName" value="FCCU_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="99">
                                    <setting name="Name" value="PlatformIsrConfig_99"/>
                                    <setting name="IsrName" value="SBSW_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="100">
                                    <setting name="Name" value="PlatformIsrConfig_100"/>
                                    <setting name="IsrName" value="HSE_MU0_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="101">
                                    <setting name="Name" value="PlatformIsrConfig_101"/>
                                    <setting name="IsrName" value="HSE_MU0_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="102">
                                    <setting name="Name" value="PlatformIsrConfig_102"/>
                                    <setting name="IsrName" value="HSE_MU0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="103">
                                    <setting name="Name" value="PlatformIsrConfig_103"/>
                                    <setting name="IsrName" value="HSE_MU1_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="104">
                                    <setting name="Name" value="PlatformIsrConfig_104"/>
                                    <setting name="IsrName" value="HSE_MU1_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="105">
                                    <setting name="Name" value="PlatformIsrConfig_105"/>
                                    <setting name="IsrName" value="HSE_MU1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="106">
                                    <setting name="Name" value="PlatformIsrConfig_106"/>
                                    <setting name="IsrName" value="HSE_MU2_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="107">
                                    <setting name="Name" value="PlatformIsrConfig_107"/>
                                    <setting name="IsrName" value="HSE_MU2_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="108">
                                    <setting name="Name" value="PlatformIsrConfig_108"/>
                                    <setting name="IsrName" value="HSE_MU2_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="109">
                                    <setting name="Name" value="PlatformIsrConfig_109"/>
                                    <setting name="IsrName" value="HSE_MU3_TX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="110">
                                    <setting name="Name" value="PlatformIsrConfig_110"/>
                                    <setting name="IsrName" value="HSE_MU3_RX_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="111">
                                    <setting name="Name" value="PlatformIsrConfig_111"/>
                                    <setting name="IsrName" value="HSE_MU3_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="112">
                                    <setting name="Name" value="PlatformIsrConfig_112"/>
                                    <setting name="IsrName" value="DDR0_SCRUB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="113">
                                    <setting name="Name" value="PlatformIsrConfig_113"/>
                                    <setting name="IsrName" value="DDR0_PHY_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="114">
                                    <setting name="Name" value="PlatformIsrConfig_114"/>
                                    <setting name="IsrName" value="CTU_FIFO_FULL_EMPTY_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="115">
                                    <setting name="Name" value="PlatformIsrConfig_115"/>
                                    <setting name="IsrName" value="CTU_M_RELOAD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="116">
                                    <setting name="Name" value="PlatformIsrConfig_116"/>
                                    <setting name="IsrName" value="CTU_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="117">
                                    <setting name="Name" value="PlatformIsrConfig_117"/>
                                    <setting name="IsrName" value="TMU_ALARM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="118">
                                    <setting name="Name" value="PlatformIsrConfig_118"/>
                                    <setting name="IsrName" value="RTC_SYS_CONT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="119">
                                    <setting name="Name" value="PlatformIsrConfig_119"/>
                                    <setting name="IsrName" value="PCIE0_ORED_DMA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="120">
                                    <setting name="Name" value="PlatformIsrConfig_120"/>
                                    <setting name="IsrName" value="PCIE0_LINK_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="121">
                                    <setting name="Name" value="PlatformIsrConfig_121"/>
                                    <setting name="IsrName" value="PCIE0_AXI_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="122">
                                    <setting name="Name" value="PlatformIsrConfig_122"/>
                                    <setting name="IsrName" value="PCIE0_PHY_DOWM_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="123">
                                    <setting name="Name" value="PlatformIsrConfig_123"/>
                                    <setting name="IsrName" value="PCIE0_PHY_UP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="124">
                                    <setting name="Name" value="PlatformIsrConfig_124"/>
                                    <setting name="IsrName" value="PCIE0_INTA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="125">
                                    <setting name="Name" value="PlatformIsrConfig_125"/>
                                    <setting name="IsrName" value="PCIE0_INTB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="126">
                                    <setting name="Name" value="PlatformIsrConfig_126"/>
                                    <setting name="IsrName" value="PCIE0_INTC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="127">
                                    <setting name="Name" value="PlatformIsrConfig_127"/>
                                    <setting name="IsrName" value="PCIE0_INTD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="128">
                                    <setting name="Name" value="PlatformIsrConfig_128"/>
                                    <setting name="IsrName" value="PCIE0_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="129">
                                    <setting name="Name" value="PlatformIsrConfig_129"/>
                                    <setting name="IsrName" value="PCIE0_PCS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="130">
                                    <setting name="Name" value="PlatformIsrConfig_130"/>
                                    <setting name="IsrName" value="PCIE0_TLP_NC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="131">
                                    <setting name="Name" value="PlatformIsrConfig_131"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_L2RAM_CLUSTER0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="132">
                                    <setting name="Name" value="PlatformIsrConfig_132"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_LIVLOCK_CLUSTER0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="133">
                                    <setting name="Name" value="PlatformIsrConfig_133"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_L2RAM_CLUSTER1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="134">
                                    <setting name="Name" value="PlatformIsrConfig_134"/>
                                    <setting name="IsrName" value="CORTEX_A53_ERR_LIVLOCK_CLUSTER1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="135">
                                    <setting name="Name" value="PlatformIsrConfig_135"/>
                                    <setting name="IsrName" value="JDC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="136">
                                    <setting name="Name" value="PlatformIsrConfig_136"/>
                                    <setting name="IsrName" value="SWT8_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="137">
                                    <setting name="Name" value="PlatformIsrConfig_137"/>
                                    <setting name="IsrName" value="SWT9_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="138">
                                    <setting name="Name" value="PlatformIsrConfig_138"/>
                                    <setting name="IsrName" value="SWT10_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="139">
                                    <setting name="Name" value="PlatformIsrConfig_139"/>
                                    <setting name="IsrName" value="SWT11_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="140">
                                    <setting name="Name" value="PlatformIsrConfig_140"/>
                                    <setting name="IsrName" value="STM8_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="141">
                                    <setting name="Name" value="PlatformIsrConfig_141"/>
                                    <setting name="IsrName" value="STM9_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="142">
                                    <setting name="Name" value="PlatformIsrConfig_142"/>
                                    <setting name="IsrName" value="STM10_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="143">
                                    <setting name="Name" value="PlatformIsrConfig_143"/>
                                    <setting name="IsrName" value="STM11_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="144">
                                    <setting name="Name" value="PlatformIsrConfig_144"/>
                                    <setting name="IsrName" value="MCSCM_INT7_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="145">
                                    <setting name="Name" value="PlatformIsrConfig_145"/>
                                    <setting name="IsrName" value="MCSCM_INT8_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="146">
                                    <setting name="Name" value="PlatformIsrConfig_146"/>
                                    <setting name="IsrName" value="MCSCM_INT9_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="147">
                                    <setting name="Name" value="PlatformIsrConfig_147"/>
                                    <setting name="IsrName" value="MCSCM_INT10_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="148">
                                    <setting name="Name" value="PlatformIsrConfig_148"/>
                                    <setting name="IsrName" value="MCSCM_INT11_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="149">
                                    <setting name="Name" value="PlatformIsrConfig_149"/>
                                    <setting name="IsrName" value="LLCE0_INT0_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="150">
                                    <setting name="Name" value="PlatformIsrConfig_150"/>
                                    <setting name="IsrName" value="LLCE0_INT1_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="151">
                                    <setting name="Name" value="PlatformIsrConfig_151"/>
                                    <setting name="IsrName" value="LLCE0_INT2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="152">
                                    <setting name="Name" value="PlatformIsrConfig_152"/>
                                    <setting name="IsrName" value="LLCE0_INT3_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="153">
                                    <setting name="Name" value="PlatformIsrConfig_153"/>
                                    <setting name="IsrName" value="LLCE0_ICSR14_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="154">
                                    <setting name="Name" value="PlatformIsrConfig_154"/>
                                    <setting name="IsrName" value="LLCE0_ICSR15_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="155">
                                    <setting name="Name" value="PlatformIsrConfig_155"/>
                                    <setting name="IsrName" value="LLCE0_ICSR16_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="156">
                                    <setting name="Name" value="PlatformIsrConfig_156"/>
                                    <setting name="IsrName" value="LLCE0_ICSR17_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="157">
                                    <setting name="Name" value="PlatformIsrConfig_157"/>
                                    <setting name="IsrName" value="LLCE0_ICSR18_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="158">
                                    <setting name="Name" value="PlatformIsrConfig_158"/>
                                    <setting name="IsrName" value="LLCE0_ICSR19_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="159">
                                    <setting name="Name" value="PlatformIsrConfig_159"/>
                                    <setting name="IsrName" value="LLCE0_ICSR20_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="160">
                                    <setting name="Name" value="PlatformIsrConfig_160"/>
                                    <setting name="IsrName" value="LLCE0_ICSR21_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="161">
                                    <setting name="Name" value="PlatformIsrConfig_161"/>
                                    <setting name="IsrName" value="LLCE0_ICSR22_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="162">
                                    <setting name="Name" value="PlatformIsrConfig_162"/>
                                    <setting name="IsrName" value="LLCE0_ICSR23_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="163">
                                    <setting name="Name" value="PlatformIsrConfig_163"/>
                                    <setting name="IsrName" value="LLCE0_ICSR24_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="164">
                                    <setting name="Name" value="PlatformIsrConfig_164"/>
                                    <setting name="IsrName" value="LLCE0_ICSR25_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="165">
                                    <setting name="Name" value="PlatformIsrConfig_165"/>
                                    <setting name="IsrName" value="LLCE0_ICSR26_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="166">
                                    <setting name="Name" value="PlatformIsrConfig_166"/>
                                    <setting name="IsrName" value="LLCE0_ICSR27_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="167">
                                    <setting name="Name" value="PlatformIsrConfig_167"/>
                                    <setting name="IsrName" value="PFE0_CH0_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="168">
                                    <setting name="Name" value="PlatformIsrConfig_168"/>
                                    <setting name="IsrName" value="PFE0_CH1_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="169">
                                    <setting name="Name" value="PlatformIsrConfig_169"/>
                                    <setting name="IsrName" value="PFE0_CH2_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="170">
                                    <setting name="Name" value="PlatformIsrConfig_170"/>
                                    <setting name="IsrName" value="PFE0_CH3_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="171">
                                    <setting name="Name" value="PlatformIsrConfig_171"/>
                                    <setting name="IsrName" value="PFE0_BMU1_BMU2_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="172">
                                    <setting name="Name" value="PlatformIsrConfig_172"/>
                                    <setting name="IsrName" value="PFE0_HIF_NC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="173">
                                    <setting name="Name" value="PlatformIsrConfig_173"/>
                                    <setting name="IsrName" value="PFE0_UT_GPT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="174">
                                    <setting name="Name" value="PlatformIsrConfig_174"/>
                                    <setting name="IsrName" value="PFE0_PMT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="175">
                                    <setting name="Name" value="PlatformIsrConfig_175"/>
                                    <setting name="IsrName" value="PFE0_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="176">
                                    <setting name="Name" value="PlatformIsrConfig_176"/>
                                    <setting name="IsrName" value="STM_TS_CH_REQ_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="177">
                                    <setting name="Name" value="PlatformIsrConfig_177"/>
                                    <setting name="IsrName" value="SIUL1_ORED_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="178">
                                    <setting name="Name" value="PlatformIsrConfig_178"/>
                                    <setting name="IsrName" value="USB0_OTG_CORE_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="179">
                                    <setting name="Name" value="PlatformIsrConfig_179"/>
                                    <setting name="IsrName" value="USB0_OTG_WKP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="180">
                                    <setting name="Name" value="PlatformIsrConfig_180"/>
                                    <setting name="IsrName" value="WKPU_GRP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="181">
                                    <setting name="Name" value="PlatformIsrConfig_181"/>
                                    <setting name="IsrName" value="PCIE1_ORED_DMA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="182">
                                    <setting name="Name" value="PlatformIsrConfig_182"/>
                                    <setting name="IsrName" value="PCIE1_STAT_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="183">
                                    <setting name="Name" value="PlatformIsrConfig_183"/>
                                    <setting name="IsrName" value="PCIE1_AXI_MSI_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="184">
                                    <setting name="Name" value="PlatformIsrConfig_184"/>
                                    <setting name="IsrName" value="PCIE1_PHY_LDOWN_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="185">
                                    <setting name="Name" value="PlatformIsrConfig_185"/>
                                    <setting name="IsrName" value="PCIE1_PHY_LUP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="186">
                                    <setting name="Name" value="PlatformIsrConfig_186"/>
                                    <setting name="IsrName" value="PCIE1_INTA_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="187">
                                    <setting name="Name" value="PlatformIsrConfig_187"/>
                                    <setting name="IsrName" value="PCIE1_INTB_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="188">
                                    <setting name="Name" value="PlatformIsrConfig_188"/>
                                    <setting name="IsrName" value="PCIE1_INTC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="189">
                                    <setting name="Name" value="PlatformIsrConfig_189"/>
                                    <setting name="IsrName" value="PCIE1_INTD_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="190">
                                    <setting name="Name" value="PlatformIsrConfig_190"/>
                                    <setting name="IsrName" value="PCIE1_MISC_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="191">
                                    <setting name="Name" value="PlatformIsrConfig_191"/>
                                    <setting name="IsrName" value="PCIE1_PCS_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="192">
                                    <setting name="Name" value="PlatformIsrConfig_192"/>
                                    <setting name="IsrName" value="PCIE1_TLP_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="193">
                                    <setting name="Name" value="PlatformIsrConfig_193"/>
                                    <setting name="IsrName" value="XRDC_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                                 <struct name="194">
                                    <setting name="Name" value="PlatformIsrConfig_194"/>
                                    <setting name="IsrName" value="XRDC_MANAGER_ERR_IRQn"/>
                                    <setting name="IsrEnabled" value="false"/>
                                    <setting name="IsrPriority" value="0"/>
                                    <setting name="IsrHandler" value="undefined_handler"/>
                                 </struct>
                              </array>
                              <array name="PlatformNvicEcucPartitionRef"/>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
               </instances>
            </functional_group>
         </functional_groups>
         <components>
            <component name="system" uuid="03e26a3f-7208-4cd4-b7d4-9c307e9fd132" type_id="system">
               <config_set_global name="SystemModel">
                  <setting name="Name" value="SystemModel"/>
                  <setting name="EcvdGenerationMethod" value="INDIVIDUAL"/>
                  <setting name="EcvdOutputPath" value=""/>
                  <setting name="EcvdGenerationTrigger" value="Generate Configuration"/>
                  <setting name="SyncFunctionalGroups" value="true"/>
                  <setting name="IgnoreComponentSuffix" value="true"/>
                  <setting name="ComponentGenerationMethod" value="FunctionalGroups"/>
                  <setting name="DefaultFunctionalGroup" value="BOARD_InitPeripherals"/>
                  <struct name="PostBuildSelectable" quick_selection="Default">
                     <setting name="Name" value="PostBuildSelectable"/>
                     <array name="PredefinedVariants">
                        <struct name="0">
                           <setting name="Name" value="BOARD_InitPeripherals"/>
                           <setting name="Path" value="/system/SystemModel/PostBuildSelectable/BOARD_InitPeripherals"/>
                           <array name="PostBuildVariantCriterionValues"/>
                        </struct>
                     </array>
                  </struct>
                  <struct name="Criterions" quick_selection="Default">
                     <setting name="Name" value="Criterions"/>
                     <array name="PostBuildVariantCriterions"/>
                  </struct>
               </config_set_global>
            </component>
         </components>
      </periphs>
   </tools>
</configuration>