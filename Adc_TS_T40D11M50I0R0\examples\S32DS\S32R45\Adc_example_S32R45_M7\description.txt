1. Example Description
    This application demonstrates the usage of the Adc Driver by Interrupt and DMA Transfer.
    The application includes a S32DS project that configures the Mcu, Mcl, Platform and Adc drivers.
    If all returned status and converted results are correct, the bStatus variable remains "TRUE" until end of the example.

    1.1 The application software functionality
        The example is divided in 3 parts:
        - Part 1: Example with SW Triggered One-Shot Conversion Mode, data conversion is updated by Interrupt.
        - Part 2: Example with SW Triggered One-Shot Conversion Mode, data conversion is updated by DMA.
        - Part 3: CTU Control Mode Example by Interrupt.

        - Mcu_Init, Mcu_InitClock, Mcu_DistributePllClock, Mcu_SetMode
                Initialize the clock sources, the clock tree and to configure the clock gating of the peripherals.
                The clock configuration that is used will enable and use the PLL as source clock.
        - Mcl_Init
                Initialize the Mcl Driver: Initialize the DMA Logic Instance and DMA Logic Channel 0.
                Logic Channel 0 is configured in EB Tresos to run in Scatter/Gather Mode, enables DMAMUX with source is DMA_IP_REQ_MUX1_SARADC1.
        - Platform_Init
                Initialize the interrupt controller and to apply the interrupt configuration according to the Platform configuration.
        - Platform_InstallIrqHandler
        - Platform_SetIrq
                Install and enable the ISR for the Adc Hardware Instance 0, CTU Hardware unit 0 and DMA Hardware 0 (Channel 16-31).
        - Adc_Init
                Initialize the Adc Driver: Initialize the Adc Hardware Instance 0 and CTU Hardware unit 0.
        - Adc_Calibrate
                Enable calibration functionality of Adc Hardware Instance 0.
        - Adc_SetupResultBuffer
                Initializes ADC driver with the group specific result buffer start address (ResultBuffer0) where the
                conversion results will be stored.
        - Adc_EnableGroupNotification
                Enables the notification mechanism for the requested ADC Channel group.
        - Adc_StartGroupConversion
                Starts the conversion of all channels of the requested ADC Channel group.
        - Adc_ReadGroup
                Reads the group conversion result of the last completed conversion round of the
                requested group and stores the channel values starting at the AdcResultBuffer0 address.
        - Adc_EnableCtuControlMode
                Enables the control mode functionality of CTU Hardware unit 0.
        - Adc_CtuMasterReload
                Start conversion of a CTU trigger source
        - Adc_DisableCtuControlMode
                Disables the control mode functionality of CTU Hardware unit 0.
        - Adc_DeInit
                Returns all ADC HW Units to a state comparable to their power on reset state.

2. Installation steps
    2.1 Hardware installation
        2.1.1 Supported boards
            - Daughter card: X-S32R45-PROC-S PCB RevB RevX5 SCH RevB1
            - Motherboard: S32GRV-PLATEVB PCB RevA SCH RevB
            - Silicon: PS32R458AAMUD 1P57D SBDA2303C (E5-Cut2.1)
        2.1.2 Connections
            - There are no specific connections needed for ADC pins
        2.1.3 Debugger
            The debugger must be connected to the J48 20-pin JTAG Cortex Debug connector.
    2.2 Software installation
        2.2.1 Importing the S32 Design Studio project
            After opening S32 Design Studio, go to "File -> New -> S32DS Project From Example" and select this example. Then click on "Finish".
            The project should now be copied into you current workspace.

3. Generating, building and running the example application
    3.1 Generating the S32 configuration
        Before running the example a configuration needs to be generated.  First go to Project Explorer View in S32 DS and select the current project. Select the "S32 Configuration Tool" menu then click on the desired configuration tool (Pins, Cocks, Peripherals etc...). Clicking on any one of those will generate all the components. Make the desired changes(if any) then click on the "S32 Configuration Tool->Update Code" button.
    3.2 Compiling the application
        Select the configuration to be built: RAM (Debug_RAM) by left clicking on the downward arrow corresponding to the build button in eclipse.
        Use Project > Build to build the project.
        Wait for the build action to be completed before continuing to the next step. Check the compiler console for error messages; upon completion, the *.elf binary file
        should be created.
    3.3 Running the application on the board
        Go to Run and select Debug Configurations. There will be a debug configuration for this project:

        Configuration Name                      Description
        ---------------------------------------------------------------------
        Adc_example_S32R45_Debug_RAM_S32Debug | Debug the RAM configuration using S32 Debugger

        Select the desired debug configuration and click on Launch. Now the perspective will change to the Debug Perspective.
        Use the controls to control the program flow.
