<?xml version='1.0'?>
<datamodel version="3.0"
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd"
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd"
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd"
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">
<!--
*   @file    Adc.xdm
*   @version 5.0.0
*
*   @brief   AUTOSAR Adc - Tresos Studio plugin schema file
*   @details This file contains the schema configuration for and Adc Tresos Studio
*            plugin.
-->
<!--
====================================================================================================
*   Project              : RTD AUTOSAR 4.4
*   Platform             : CORTEXM
*   Peripheral           : ADC_SAR
*   Dependencies         : none
*
*   Autosar Version      : 4.4.0
*   Autosar Revision     : ASR_REL_4_4_REV_0000
*   Autosar Conf.Variant :
*   SW Version           : 5.0.0
*   Build Version        : S32_RTD_5_0_0_D2410_ASR_REL_4_4_REV_0000_20241031

*   (c) Copyright 2020-2024 NXP
*   All Rights Reserved.
====================================================================================================
-->
  <d:ctr type="AUTOSAR" factory="autosar"
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd"
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd"
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="TS_T40D11M50I0R0" type="AR-PACKAGE">
        <a:a name="UUID" value="ECUC:86b36511-1da3-43b9-9687-64290e0ae1e1"/>
        <d:lst type="ELEMENTS">
          <!-- ECUC_Adc_00462 -->
          <!--  /** @implements Adc_Object */ -->
          <d:chc name="Adc" type="AR-ELEMENT" value="MODULE-DEF">
            <v:ctr type="MODULE-DEF">
              <a:a name="RELEASE" value="asc:4.4"/>
              <a:a name="ADMIN-DATA" type="ADMIN-DATA">
                <ad:ADMIN-DATA>
                  <ad:DOC-REVISIONS>
                    <ad:DOC-REVISION>
                      <ad:REVISION-LABEL>4.6.0</ad:REVISION-LABEL>
                      <ad:ISSUED-BY>AUTOSAR</ad:ISSUED-BY>
                      <ad:DATE>2014-10-31</ad:DATE>
                    </ad:DOC-REVISION>
                  </ad:DOC-REVISIONS>
                </ad:ADMIN-DATA>
              </a:a>
              <a:a name="POSTBUILDVARIANTSUPPORT" value="true"/>
              <a:a name="DESC"
                   value="EN: Configuration of the Adc (Analog Digital Conversion) module."/>
              <a:a name="LOWER-MULTIPLICITY" value="1"/>
              <a:a name="UPPER-MULTIPLICITY" value="1"/>
              <a:a name="UUID" value="ECUC:e209b42a-d115-487a-a618-da9b66535c94"/>
              <v:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN">
                <a:a name="DESC"
                     value="Indicates whether a module implementation has or plans to have (i.e., introduced at link or post-build time) new post-build variation points."/>
                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                     type="IMPLEMENTATIONCONFIGCLASS">
                  <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                </a:a>
                <a:a name="LABEL" value="Post Build Variant Used"/>
                <a:a name="ORIGIN" value="EB"/>
                <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                <a:da name="DEFAULT" value="false"/>
                <a:da name="READONLY" value="false"/>
                <a:da name="TOOLTIP"
                      value="Indicates whether a module implementation has or plans to have (i.e., introduced at link or post-build time) new post-build variation points."/>
              </v:var>

                <!--  @implements ConfigVariant_Object  -->
                <v:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION">
                  <a:a name="DESC">
                    <a:v>
                      <![CDATA[EN:<html><p>
                           Configuration classes.
                           Enable the parameters that are editable for specific configuration classes
                           </p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v class="PreCompile">VariantPostBuild</icc:v>
                      <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Config Variant"/>
                  <a:a name="UUID" value="ECUC:764babe9-0099-4db0-9716-8e8836a56d6b"/>
                  <a:da name="DEFAULT" value="VariantPostBuild"/>
                  <a:da name="RANGE">
                    <a:v>VariantPostBuild</a:v>
                    <a:v>VariantPreCompile</a:v>
                  </a:da>
                </v:var>


                <!-- ECUC_Adc_00390  -->
                <!--  @implements AdcConfigSet_Object  -->
                <v:ctr name="AdcConfigSet"  type="IDENTIFIABLE">
                  <a:a name="DESC">
                       <a:v><![CDATA[EN:<html><p>This container contains the configuration parameters and sub containers of the AUTOSAR Adc module.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="UUID" value="ECUC:205feb81-b786-4bda-a5cb-b6e6d818b5fc"/>

                  <!-- SWS_Adc_00138 -->
                  <!-- /** @implements AdcHwUnit_Object */ -->
                  <v:lst name="AdcHwUnit" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <a:da name="INVALID" type="XPath">
                       <a:tst expr="num:i(count(node:current()/*)) &gt; ecu:get(&apos;Adc.AdcConfigSet.AdcHwUnit&apos;)"
                       true="Maximum ADC Hardwares available for the selected derivative was exceeded."/>
                    </a:da>

                    <!-- AdcHwUnit Big container -->
                    <!-- ECUC_Adc_00242 -->
                    <v:ctr name="AdcHwUnit" type="IDENTIFIABLE">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>This container contains the Driver configuration (parameters) depending on grouping of channels. This container could contain HW specific parameters which are not defined in the Standardized Module Definition. They must be added in the Vendor Specific Module Definition.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="REQUIRES-INDEX" value="true"/>
                      <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="UUID" value="ECUC:d9d0ce36-e2f7-48be-8a49-b526ced3ff20"/>
                      <a:da name="INVALID" type="XPath">
                        <a:tst expr="text:uniq(../*/AdcHwUnit/*/@name, @name)" false="Duplicate Hardware Unit name"/>
                      </a:da>

                      <!-- AdcHwUnitId -->
                      <!-- ECUC_Adc_00389 -->
                      <v:var name="AdcHwUnitId" type="ENUMERATION">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>Numeric ID of the HW Unit. This symbolic name allows accessing Hw Unit data. Enumeration literals are defined vendor specific.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="LABEL" value="Adc Hardware Unit"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:f8f69c4d-d49e-4c7f-8651-5f7cfc5e352d"/>
                      <a:da name="DEFAULT" type="XPath" expr="concat('ADC', string(node:fallback(node:current()/../@index,'0')))"/>
                      <a:da name="RANGE">
                          <a:v>ADC0</a:v>
                          <a:v>ADC1</a:v>
                      </a:da>
                        <a:da name="INVALID" type="XPath">
                            <a:tst expr="text:uniq(../../*/AdcHwUnitId, .)" false="Duplicate Hw Unit id"/>
                            <a:tst expr="(num:i(count(ecu:list(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after((.),'ADC'),'.AdcChannel.AdcChannelName'))))=0)"
                                 true="This hardware unit does not support any ADC channels, please select another hardware unit."/>
                            <a:tst expr="(../AdcTransferType = 'ADC_INTERRUPT') and (count(../AdcGroup/*[AdcWithoutInterrupts='false']) > 0 ) and (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/AdcNormalInterruptEnable = 'false') and (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/AdcInjectedInterruptEnable = 'false')"
                                   true="This Unit has configured some Groups that need to use interrupts, so the associated End of Conversion interrupt must be enabled in Adc/AdcInterrupts container."/>
                            <a:tst expr="(count(../AdcChannel/*[AdcEnableThresholds='true']) > 0 ) and (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/WdgThresholdEnable = 'false')"
                                   true="This Unit has configured some Channels that have enabled the watchdog feature and need to use interrupts, so the associated Watchdog interrupt must be enabled in Adc/AdcInterrupts container."/>
                            <!-- The Unit is NOT using interrupt or using DMA, but interrupt is being enabled -->
                            <a:tst expr="(
                                          ((../AdcTransferType = 'ADC_INTERRUPT') and (count(../AdcGroup/*[AdcWithoutInterrupts = 'false']) = 0))
                                          or
                                          (../AdcTransferType = 'ADC_DMA')
                                        )
                                        and
                                        (
                                           (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/AdcNormalInterruptEnable = 'true')
                                           or
                                           (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/AdcInjectedInterruptEnable = 'true')
                                        )"
                                  true="All Groups in this unit configured as Groups Without Interrupt or using DMA, so please disable the associated End of Conversion interrupts in Adc/AdcHwConfiguration container."/>
                            <a:tst expr="(../AdcTransferType = 'ADC_DMA') and (../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()]/DmaTransferEnable = 'false')" 
                                true="DMA transfer must be enabled from ADCHwConfiguration for the coresponding HwUnitId"/>
                        </a:da>
                      </v:var>

                      <!-- AdcLogicalUnitId -->
                      <v:var name="AdcLogicalUnitId" type="INTEGER">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>Specifies the Logical id of the Hardware Unit.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="LABEL" value="Adc Logical Unit ID"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:f8f69c4d-d49e-4c7f-8651-5f7cfc5e3534"/>
                        <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, '0')"/>
                        <a:da name="RANGE" type="XPath">
                         <a:tst expr="(. &gt;= 0) and (. &lt; num:i(count(node:current()/../../*)))"
                         false="Value out of range: must be in range 0 to N-1 (N is number of configured units). Use the Calc button to calculate correct default value."/>
                         <a:tst expr="text:uniq(../../*/AdcLogicalUnitId, .)"
                         false="Duplicated value, AdcLogicalUnitId must be unique across all units. Use the Calc button to calculate correct default value."/>
                        </a:da>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;ecu:get(&apos;Adc.AdcConfigSet.AdcHwUnit&apos;)"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>

                      <!-- AdcTransferType -->
                      <v:var name="AdcTransferType" type="ENUMERATION">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>Select the Interrupt or Dma transfer Type.
                          If DMA is required for AdcTransferType, it is recommended to keep AdcWithoutInterrupts as false, otherwise, DMA will not be configured and it will be user responsibility to read the results from registers directly by calling Adc_ReadGroup.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Adc Transfer Type"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:6ec8b4cb-1d4e-4c34-a80f-db2d5bc95df9"/>
                        <a:da name="DEFAULT" value="ADC_INTERRUPT"/>
                        <a:da name="RANGE">
                          <a:v>ADC_INTERRUPT</a:v>
                          <a:v>ADC_DMA</a:v>
                        </a:da>
                        <a:a name="EDITABLE" value="true"/>
                        <a:da name="INVALID" type="XPath">
                          <a:tst expr="(.= 'ADC_DMA') and ((ecu:get(&apos;AdcDMAPresent&apos;) = 'FALSE'))"
                          true="DMA mode is not supported."/>
                          <a:tst expr="(.= 'ADC_DMA') and (../../../../AutosarExt/AdcEnableDmaTransferMode ='false')"
                          true="DMA functionality must be globally enabled in Adc/AutosarExt/AdcEnableDmaTransferMode before configuring DMA transfer for this HW unit."/>
                          <a:tst expr="(not(node:fallback(../../*/AdcTransferType,'ADC_INTERRUPT') = 'ADC_DMA')) and (../../../../AutosarExt/AdcEnableDmaTransferMode ='true')"
                          true="At least one ADC Hw Unit should be configured with Transfer Type ADC_DMA when 'Adc Enable DMA support' is true."/>
                        </a:da>
                      </v:var>

                      <!-- Adc Dma Channel Id -->
                      <v:ref name="AdcDmaChannelId" type="REFERENCE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>ID of the DMA channel used to transfer the data.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="LABEL" value="Select Dma Channel"/>
                        <a:a name="UUID" value="ECUC:64c1b027-6496-4954-ad34-13577adc9cdf"/>
                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcl/MclConfig/dmaLogicChannel_Type"/>
                        <a:a name="EDITABLE" type="XPath" expr="(../AdcTransferType ='ADC_DMA')"/>
                        <a:da name="INVALID" type="XPath">
                          <a:tst expr="not(node:refvalid(.)) and (../AdcTransferType ='ADC_DMA')" true="Invalid XPath or empty reference. Need to select Dma Channel when Transfer Type is DMA"/>
                        </a:da>
                      </v:ref>

                      <!-- Adc Counting Dma Channel Id -->
                      <v:ref name="AdcCountingDmaChannelId" type="REFERENCE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>
                          Configurable only when Transfer Type is DMA and at least one group of hw unit has:
                          AdcEnableOptimizeDmaStreamingGroups enabled with more than one ADC channel
                          OR has selected Without Interrupts, ACCESS_MODE_STREAMING and Group Streaming Results Reorder and number of channels > 1
                          OR has selected Without Interrupts, ACCESS_MODE_STREAMING and a single channel - for this case, AutosarExt Adc Enable Group Streaming Results Reorder feature must be enabled, but can be disabled at group level.
                          Linked to DMA channel by ADC driver, to count the number of samples converted in streaming mode</p></html>]]></a:v>
                        </a:a>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="LABEL" value="Select Adc Streaming Dma Channel"/>
                        <a:a name="UUID" value="ECUC:64c1b027-6496-4954-ad34-13577a5c9cdf"/>
                        <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcl/MclConfig/dmaLogicChannel_Type"/>
                        <a:a name="EDITABLE" type="XPath" expr="(../AdcTransferType ='ADC_DMA') and (num:i(count(../AdcGroup/*[(((AdcEnableOptimizeDmaStreamingGroups = 'true') or ((AdcWithoutInterrupts = 'true') and (AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING') and (AdcStreamResultGroup = 'true'))) and (count(AdcGroupDefinition/*) > 1)) or ((AdcWithoutInterrupts = 'true') and (AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING') and (count(AdcGroupDefinition/*) = 1))])) > 0)"/>
                        <a:da name="INVALID" type="XPath">
                          <a:tst expr="not(node:refvalid(.)) and (../AdcTransferType ='ADC_DMA') and (num:i(count(../AdcGroup/*[(((AdcEnableOptimizeDmaStreamingGroups = 'true') or ((AdcWithoutInterrupts = 'true') and (AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING') and (AdcStreamResultGroup = 'true'))) and (count(AdcGroupDefinition/*) > 1)) or ((AdcWithoutInterrupts = 'true') and (AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING') and (count(AdcGroupDefinition/*) = 1))])) > 0)"
                                true="Invalid XPath or empty reference. Need to select Counting Dma Channel when Transfer Type is DMA and at least one group of hw unit
                                      has AdcEnableOptimizeDmaStreamingGroups enabled with more than one ADC channels in AdcGroupDefinition
                                      OR has selected Without Interrupts, ACCESS_MODE_STREAMING and Group Streaming Results Reorder and number of channels > 1
                                      OR has selected Without Interrupts, ACCESS_MODE_STREAMING and a single channel - for this case, AutosarExt Adc Enable Group Streaming Results Reorder feature must be enabled, but can be disabled at group level. "/>
                          <a:tst expr="(not(node:empty(.)) and (. = ../AdcDmaChannelId))"
                                 true="Counting DMA channel and DMA channel need to be different"/>
                        </a:da>
                      </v:ref>

                      <!-- AdcClockSource -->
                      <!-- ECUC_Adc_00087 -->
                      <v:var name="AdcClockSource" type="ENUMERATION">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>The ADC module specific clock input for the conversion unit can statically be configured to select different clock sources if provided by hardware. Enumeration literals are defined vendor specific.
                            This parameter is not used by the current implementation.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PostBuild">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="LABEL" value="Adc Source Clock"/>
                        <a:a name="UUID" value="ECUC:947dbcca-8b33-4b4e-8d73-c2a9d6fb8d96"/>
                        <a:da name="DEFAULT" value="BUS_CLOCK"/>
                        <a:da name="READONLY" value="true"/>
                        <a:da name="RANGE">
                            <a:v>BUS_CLOCK</a:v>
                        </a:da>
                      </v:var>

                    <!-- AdcPrescale -->
                    <!-- ECUC_Adc_00088 -->
                      <v:var name="AdcPrescale" type="INTEGER">
                        <a:a name="DESC">
                             <a:v><![CDATA[EN:<html><p>Optional ADC module specific clock prescale factor, if supported by hardware.
                              ImplementationType: Adc_PrescaleType.
                              The Prescaler value for NORMAL mode. Only the following are allowed:
                                   <ul>
                                   <li>1: ADC module clock frequency.</li>
                                   <li>2: ADC module clock frequency / 2.</li>
                                   </ul>
                                   </p></html>]]>
                             </a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v class="PostBuild">VariantPostBuild</icc:v>
                          <icc:v class="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Adc Prescaler Value"/>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:d1dd89fe-fdb8-43e4-a51d-927bd64e8e87"/>
                        <a:da name="DEFAULT" value="1"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=2"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                        <a:da name="RANGE" type="Range">
                          <a:tst expr="&lt;=65535"/>
                          <a:tst expr="&gt;=0"/>
                        </a:da>
                      </v:var>

                      <!-- AdcAltPrescaler -->
                      <v:var name="AdcAltPrescale" type="INTEGER">
                        <a:a name="DESC">
                             <a:v><![CDATA[EN:<html><p>The Prescaler value for ALTERNATE mode. This feature can be configured if Adc Set Clock Mode API from General/AutosarExt is enabled.
                                    <ul>
                                    <li>1: ADC module clock frequency.</li>
                                    <li>2: ADC module clock frequency / 2.</li>
                                    </ul>
                                   </p></html>]]>
                             </a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Adc Alternate Prescale"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:c227b7fb-ea26-4387-be77-14b370bfa914"/>
                        <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../AutosarExt/AdcEnableDualClockMode,'true') = 'true' and node:exists(../AdcPrescale)"/>
                        </a:a>
                        <a:da name="DEFAULT" value="2"/>
                        <a:da name="RANGE" type="XPath">
                          <a:tst expr="(node:fallback(../../../../AutosarExt/AdcEnableDualClockMode,'true') = 'true') and node:exists(../AdcPrescale) and  (num:i(.) = num:i(node:fallback(../AdcPrescale, 1)))"
                                 true="Prescale value for NORMAL mode and ALTERNATE mode should be different if Adc Set Clock Mode API is enabled in Adc/AutosarExt container."/>
                        </a:da>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=2"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                      </v:var>

                    <!-- AdcCalibrationPrescale -->
                    <v:var name="AdcCalibrationPrescale" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Specifies the used clock input for calibration. This feature can be configured if Adc Enable Calibration API from General/AutosarExt is enabled.
                                <ul>
                                <li>1: ADC module clock frequency.</li>
                                <li>2: ADC module clock frequency / 2.</li>
                                </ul>
                               </p></html>]]>
                        </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                         type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Calibration Prescale"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:43c026fc-c64d-4dd4-b273-14b370bfa145"/>
                      <a:a name="EDITABLE" type="XPath">
                        <a:tst expr="node:fallback(../../../../AutosarExt/AdcEnableCalibration,'true') = 'true'"/>
                      </a:a>
                      <a:da name="DEFAULT" value="2"/>
                      <a:da name="RANGE" type="XPath">
                        <a:tst expr="(. != 1) and (. != 2)" true="Only values 1, 2 are allowed"/>
                      </a:da>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&lt;=2"/>
                          <a:tst expr="&gt;=1"/>
                        </a:da>
                    </v:var>

                    <!-- CPR_RTD_00055.adc -->
                    <!--  @implements AdcPowerDownDelay_Object -->
                    <v:var name="AdcPowerDownDelay" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>The delay between the power down bit reset and the starting of conversion.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Power Down Delay"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:c5548505-24f7-4014-8d31-caeb1326a4dd"/>
                      <a:da name="DEFAULT" value="15"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=0"/>
                        <a:tst expr="&lt;=255"/>
                      </a:da>
                    </v:var>

                    <!-- CPR_RTD_00055.adc -->
                    <!-- AdcAltPowerDownDelay -->
                    <v:var name="AdcAltPowerDownDelay" type="INTEGER">
                      <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>The delay between the power down bit reset and the starting of conversion when ADC runs on low power system frequency.
                          This node is EDITABLE only if "Adc Set Clock Mode API" is enabled</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Alternate Power Down Delay"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:c227b7fb-ea26-4387-be77-14b370bfa117"/>
                      <a:a name="EDITABLE" type="XPath">
                        <a:tst expr="node:fallback(../../../../AutosarExt/AdcEnableDualClockMode,'true') = 'true'"/>
                      </a:a>
                      <a:da name="DEFAULT" value="15"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=0"/>
                        <a:tst expr="&lt;=255"/>
                      </a:da>
                    </v:var>
                    <!-- AdcAutoClockOff -->
                    <v:var name="AdcAutoClockOff" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Enables/disables the auto-clock-off features.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Auto Clock Off"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:55426ced-9131-4adf-a3e5-92c6db909171"/>
                      <a:da name="DEFAULT" value="false"/>
                    </v:var>

                    <!-- AdcBypassSampling -->
                    <v:var name="AdcBypassSampling" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Select whether the presampling is followed by the comparison.
                        Disabled: The presampling will be followed by sampling the selected input.
                        Enabled: The presampling will be followed by the successive approximation algorithm and the conversion data will be written to the conversion data register of the selected input.
                        </p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Bypass Sampling"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:cdb243d9-cb16-4995-b4b0-fb2fcb499bb2"/>
                      <a:da name="DEFAULT" value="false"/>
                    </v:var>

                      <!-- AdcHwUnitOverwriteEn -->
                      <v:var name="AdcHwUnitOverwriteEn" type="BOOLEAN">
                        <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Specifies whether a conversion data register accepts new data before the current conversion data has been read. When enabled, the new conversion result overwrites the older data, regardless of the validity of the older conversion data. When CDRn[VALID] = 1, the conversion data has not been read.
                          </p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Adc Result Overwrite Enable"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:5fba7e71-1cc8-49fc-9172-4c137177e1f0"/>
                        <a:da name="DEFAULT" value="true"/>
                      </v:var>

                    <!-- AdcPresamplingInternalSignal0 -->
                    <v:var name="AdcPresamplingInternalSignal0" type="ENUMERATION">
                      <a:a name="DESC">
                          <a:v>
                              <![CDATA[EN: <html>
                                  Select the Adc presampling internal voltage for channels 0-31, AdcPresamplingInternalSignal0 shall take the following values:
                                  <ul>
                                  <li>DVDD0p8/2    - Pre-sample voltage - 1.</li>
                                  <li>AVDD1p8/4    - Pre-sample voltage - 2.</li>
                                  <li>VREFL_1p8    - Pre-sample voltage - 3.</li>
                                  <li>VREFH_1p8    - Pre-sample voltage - 4.</li>
                                  </ul>
                                 </html>]]>
                          </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Presampling channel (0 - 31)"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:37ce3a5a-7a2d-4af0-8644-d8bfd4d6c797"/>
                      <a:da name="DEFAULT" value="DVDD"/>
                      <a:da name="RANGE">
                        <a:v>DVDD</a:v>
                        <a:v>AVDD</a:v>
                        <a:v>VREFL</a:v>
                        <a:v>VREFH</a:v>
                      </a:da>
                      <a:da name="EDITABLE" type="XPath" expr="text:contains(ecu:get(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after(string(node:fallback(../AdcHwUnitId,'ADC0')), 'ADC'),'.AdcPscrSupported')), 'PREVAL0')"></a:da>
                    </v:var>

                    <!-- AdcPresamplingInternalSignal1 -->
                    <v:var name="AdcPresamplingInternalSignal1" type="ENUMERATION">
                      <a:a name="DESC">
                          <a:v>
                              <![CDATA[EN: <html>
                                  Select the Adc presampling internal voltage for channels 32-63, AdcPresamplingInternalSignal0 shall take the following values:
                                  <ul>
                                  <li>DVDD0p8/2    - Pre-sample voltage - 1.</li>
                                  <li>AVDD1p8/4    - Pre-sample voltage - 2.</li>
                                  <li>VREFL_1p8    - Pre-sample voltage - 3.</li>
                                  <li>VREFH_1p8    - Pre-sample voltage - 4.</li>
                                  </ul>
                                 </html>]]>
                          </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Presampling channel (32 - 63)"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:37ce3a5a-7a2d-4af0-8644-d8bfd4d6c798"/>
                      <a:da name="DEFAULT" value="DVDD"/>
                      <a:da name="RANGE">
                        <a:v>DVDD</a:v>
                        <a:v>AVDD</a:v>
                        <a:v>VREFL</a:v>
                        <a:v>VREFH</a:v>
                      </a:da>
                      <a:da name="EDITABLE" type="XPath" expr="text:contains(ecu:get(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after(string(node:fallback(../AdcHwUnitId,'ADC0')), 'ADC'),'.AdcPscrSupported')), 'PREVAL1')"></a:da>
                    </v:var>

                    <!-- AdcPresamplingInternalSignal2 -->
                    <v:var name="AdcPresamplingInternalSignal2" type="ENUMERATION">
                      <a:a name="DESC">
                          <a:v>
                              <![CDATA[EN: <html>
                                  Select the Adc presampling internal voltage for channels 64-95, AdcPresamplingInternalSignal0 shall take the following values:
                                  <ul>
                                  <li>DVDD0p8/2    - Pre-sample voltage - 1.</li>
                                  <li>AVDD1p8/4    - Pre-sample voltage - 2.</li>
                                  <li>VREFL_1p8    - Pre-sample voltage - 3.</li>
                                  <li>VREFH_1p8    - Pre-sample voltage - 4.</li>
                                  </ul>
                                 </html>]]>
                          </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Adc Presampling channel (64 - 95)"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:37ce3a5a-7a2d-4af0-8644-d8bfd4d6c7942"/>
                      <a:da name="DEFAULT" value="DVDD"/>
                      <a:da name="RANGE">
                        <a:v>DVDD</a:v>
                        <a:v>AVDD</a:v>
                        <a:v>VREFL</a:v>
                        <a:v>VREFH</a:v>
                      </a:da>
                      <a:da name="EDITABLE" type="XPath" expr="text:contains(ecu:get(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after(string(node:fallback(../AdcHwUnitId,'ADC0')), 'ADC'),'.AdcPscrSupported')), 'PREVAL2')"></a:da>
                    </v:var>
                    <!-- AdcHwUnitUsrOffset -->
                    <v:var name="AdcHwUnitUsrOffset" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>The ADC can compensate a maximum of +/-15LSB at 12b offset by programming OFFSET_USER field in USROFSGN register.
                            The field is in 2's complement format and stored with an 8x multiplication. Thus, if a value of +/-X is stored in this field, it leads to
                            a final result adjusted with +/-(X/8) from its unprogrammed version. It is required to keep the 3-LSBs zero to avoid any fractional
                            subtraction-related errors.
                            ADC_OUTPUT_NEW = ADC_OUTPUT_OLD - (OFFSET_USER/8).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="User Offset"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:16426534-25ba-4d15-a8f6-30f4879cb9d2"/>
                          <a:da name="DEFAULT" value="0"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=0"/>
                            <a:tst expr="&lt;=255"/>
                          </a:da>
                        </v:var>

                        <!-- AdcHwUnitUsrGain -->
                        <v:var name="AdcHwUnitUsrGain" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>The ADC can compensate a maximum of +/-63 LSB at 12b gain by programming GAIN_USER field in USROFSGN register.
                              The field is in 2's complement format and stored with an 8x multiplication. Thus, if a value of +/- Y is stored in this field, it leads to a
                              final result adjusted with +/-(Y/8) LSB from its unprogrammed version. It is required to keep the 3-LSBs zero to avoid any fractional
                              subtraction/multiplication related errors.
                              ADC_OUTPUT_NEW = ADC_OUTPUT_OLD * (1 + (GAIN_USER / (8 * 4096))).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="User Gain"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:16426524-25ba-4d15-a8f6-30f4879cb9d2"/>
                          <a:da name="DEFAULT" value="0"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=0"/>
                            <a:tst expr="&lt;=65535"/>
                          </a:da>
                        </v:var>
                        <!-- AdcHwUnitDmaClearSource -->
                        <v:var name="AdcHwUnitDmaClearSource" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select the DMA request clearing source.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="DMA Clear Source"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:8e2f1435-2754-4f03-b094-073b363aaa66"/>
                          <a:da name="DEFAULT" value="DMA_REQ_CLEAR_ON_ACK"/>
                          <a:da name="RANGE">
                            <a:v>DMA_REQ_CLEAR_ON_ACK</a:v>
                            <a:v>DMA_REQ_CLEAR_ON_READ</a:v>
                          </a:da>
                          <a:a name="EDITABLE" type="XPath" expr="(../AdcTransferType ='ADC_DMA')"/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="(.= 'DMA_REQ_CLEAR_ON_READ') and (num:i(count(../AdcGroup/*[(AdcEnableOptimizeDmaStreamingGroups = 'true') and (count(AdcGroupDefinition/*) > 1)])) > 0)"
                            true="DMA_REQ_CLEAR_ON_ACK is required if current HW Unit has at least one group more than one channel that enable feature 'Adc Enable Optimize DMA Streaming Groups'.
                            Because the Counting Dma Channel made a dummy transfer to count how many sample was completed, so CLEAR_ON_READ can not disable HW Request from Adc to Dma."/>
                          </a:da>
                        </v:var>
                        <v:ctr name="AdcSelfTestThresholdConfiguration" type="IDENTIFIABLE">
                          <a:a name ="DESC">
                          <a:v>
                              <![CDATA[<html><p>These values define the limits for results measured at each step of the self-test algorithms. If measured values are outside these ranges, the self test fails.
                              When selecting the values, please take into account ADC resolution and reference voltage used.
                              For more details please refer to reference manual.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="UUID" value="ECUC:0cb2283c-677d-434e-80a6-30f6179cb199"/>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:da name="ENABLE" value="true"/>
                          <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="EDITABLE" type="XPath">
                            <a:tst expr="(../../../../AutosarExt/AdcEnableSelfTest = 'true')"/>
                          </a:a>
                          <!-- AdcSTAW0RSelfTestHighThresholdValue -->
                          <v:var name="AdcSTAW0RSelfTestHighThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest high threshold value for Algorithm S0.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW0R SelfTest High Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW0RDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb100"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW0RDefaultValue')[1] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW0RSelfTestLowThresholdValue -->
                          <v:var name="AdcSTAW0RSelfTestLowThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest low threshold value for Algorithm S0.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW0R SelfTest Low Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW0RDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb101"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW0RDefaultValue')[2] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW1ARSelfTestHighThresholdValue -->
                          <v:var name="AdcSTAW1ARSelfTestHighThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest high threshold value for Algorithm S1 - integer part.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW1AR SelfTest High Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW1ARDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb103"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW1ARDefaultValue')[1] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW1ARSelfTestLowThresholdValue -->
                          <v:var name="AdcSTAW1ARSelfTestLowThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest low threshold value for Algorithm S1 - integer part.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW1AR SelfTest Low Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                            <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW1ARDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                              <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb104"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW1ARDefaultValue')[2] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW1BRSelfTestLowThresholdValue -->
                          <v:var name="AdcSTAW1BRSelfTestHighThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest high threshold value for Algorithm S1 - fractional part.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW1BR SelfTest High Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW1BRDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb105"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW1BRDefaultValue')[1] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW1BRSelfTestLowThresholdValue -->
                          <v:var name="AdcSTAW1BRSelfTestLowThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest low threshold value for Algorithm S1 - fractional part.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW1BR SelfTest Low Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW1BRDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb106"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:list('Adc.AdcSTAW1BRDefaultValue')[2] )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW2RSelfTestLowThresholdValue -->
                          <v:var name="AdcSTAW2RSelfTestLowThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest low threshold value for Algorithm S2.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW2R SelfTest Low Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW2RDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb107"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:get('Adc.AdcSTAW2RDefaultValue') )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW4RSelfTestHighThresholdValue -->
                          <v:var name="AdcSTAW4RSelfTestHighThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest high threshold value for Algorithm C0. Low threshold value is two's-complement of high threshold value and automatically calculated.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW4R SelfTest High Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW4RDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb108"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:get('Adc.AdcSTAW4RDefaultValue') )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                          <!-- AdcSTAW5RSelfTestHighThresholdValue -->
                          <v:var name="AdcSTAW5RSelfTestHighThresholdValue" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>SelfTest high threshold value for Algorithm C1 to C11. Low threshold value is two's-complement of high threshold value and automatically calculated</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="LABEL" value="Adc STAW5R SelfTest High Threshold Value"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../AutosarExt/AdcEnableSelfTest = 'true') and (not(text:contains(ecu:get(concat('Adc.AdcSTAW5RDefaultValue',node:when(text:contains(string(node:fallback(../../AdcHwUnitId, 'ADC0')), 'AE'), 'AE', ''))), '0xFFFFFFFF')))"/>
                            </a:a>
                            <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb109"/>
                            <a:da name="DEFAULT"  type="XPath" expr="num:hextoint( ecu:get('Adc.AdcSTAW5RDefaultValue') )"/>
                            <a:da name="DEFAULT_RADIX" value="HEX"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(. &gt;= 0) and (. &lt;=(bit:shl(1, ecu:get('Adc.AdcConfigSet.AdcHwUnit.AdcMaxResolution')) - 1))"
                                      false="Value out of range. Must be less than ((2 ^ MAX_RESOLUTION) - 1)."/>
                            </a:da>
                          </v:var>
                        </v:ctr>

                        <v:ctr name="AdcNormalConvTimings" type="IDENTIFIABLE">
                          <a:a name ="DESC">
                          <a:v>
                              <![CDATA[<html><p>Selects Normal values used for programming CTR Conversion Timing Registers in Adc_SetClockMode API and also when AdcConvTimeOnce option is enabled.
                              This node is EDITABLE only if "Adc Conversion Time Once" is enabled</p></html>]]></a:v>
                          </a:a>
                          <a:a name="UUID" value="ECUC:0cb2283c-677d-434e-80a6-169b5c6e63e3"/>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'"/>
                        </a:a>

                        <!-- AdcSamplingDurationNormal0 -->
                        <v:var name="AdcSamplingDurationNormal0" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select the Normal Sampling Duration for channels 0-31 when calling Adc_SetClockMode(ADC_NORMAL) for CTR0 register.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Adc Unit Normal Sampling Duration 0"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'"/>
                          </a:a>
                          <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a8f6-30f4879cb9d2"/>
                          <a:da name="DEFAULT" value="22"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=22"/>
                            <a:tst expr="&lt;=255"/>
                          </a:da>
                        </v:var>

                        <!-- AdcSamplingDurationNormal1 -->
                        <v:var name="AdcSamplingDurationNormal1" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select the Normal Sampling Duration for channels 32-63 when calling Adc_SetClockMode(ADC_NORMAL) for CTR1 register.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Adc Unit Normal Sampling Duration 1"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'"/>
                          </a:a>
                          <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a856-30f6879cb9d2"/>
                          <a:da name="DEFAULT" value="22"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=22"/>
                            <a:tst expr="&lt;=255"/>
                          </a:da>
                        </v:var>
                      </v:ctr>

                      <v:ctr name="AdcAlternateConvTimings" type="IDENTIFIABLE">
                        <a:a name ="DESC">
                          <a:v>
                          <![CDATA[<html><p>Selects Alternate values used in Adc_SetClockMode API for programming CTR Conversion Timing Registers.
                          This container is EDITABLE only if "Adc Conversion Time Once" and "Adc Set Clock Mode API" are enabled. Hardware averaging functionality is also available for configuration if supported.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="UUID" value="ECUC:0cb2283c-677d-434e-80a6-169b5c6e63e5"/>
                        <a:a name="OPTIONAL" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'and (../../../../AutosarExt/AdcEnableDualClockMode = 'true')"/>
                        </a:a>

                        <!-- AdcSamplingDurationAlt0 -->
                        <v:var name="AdcSamplingDurationAlt0" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select the Alternate Sampling Duration when calling Adc_SetClockMode(ADC_NORMAL) for CTR0 register.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Adc Unit Alternate Sampling Duration 0"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'
                          and node:fallback(../../../../../AutosarExt/AdcEnableDualClockMode,'false') = 'true'"/>
                          </a:a>
                          <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f6179cb9d2"/>
                          <a:da name="DEFAULT" value="22"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=22"/>
                            <a:tst expr="&lt;=255"/>
                          </a:da>
                        </v:var>

                        <!-- AdcSamplingDurationAlt1 -->
                        <v:var name="AdcSamplingDurationAlt1" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select the Alternate Sampling Duration when calling Adc_SetClockMode(ADC_NORMAL) for CTR1 register.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Adc Unit Alternate Sampling Duration 1"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="EDITABLE" type="XPath">
                          <a:tst expr="node:fallback(../../../../../AutosarExt/AdcConvTimeOnce,'true') = 'true'
                          and node:fallback(../../../../../AutosarExt/AdcEnableDualClockMode,'false') = 'true'"/>
                          </a:a>
                          <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a6f6-30f3879cb9d2"/>
                          <a:da name="DEFAULT" value="22"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&gt;=22"/>
                            <a:tst expr="&lt;=255"/>
                          </a:da>
                        </v:var>
                      </v:ctr>

                    <!-- AdcChannel Big container -->
                    <!-- ECUC_Adc_00268 -->
                    <!-- /** @implements AdcChannel_Object */ -->
                      <v:lst name="AdcChannel" type="MAP">
                        <a:da name="MIN" value="1"/>
                        <v:ctr name="AdcChannel" type="IDENTIFIABLE">
                           <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>This container contains the channel configuration (parameters) depending on the hardware capability.
                              The organization of this data structure could contain dependencies to the microcontroller so this is left up to the implementer and its location is left up to the configuration.
                              Note: Since a AdcChannel can be part of several AdcGroups, this container is not realized as a subcontainer of AdcGroup but instead as a subcontainer of AdcHwUnit.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="REQUIRES-INDEX" value="true"/>
                          <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="UUID" value="ECUC:ef75f609-d83e-411c-aa87-76f21a85cf68"/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="text:uniq(../../../*/AdcChannel/*/@name, @name)" false="Duplicate AdcChannelNames, Channel Symbolic names must be unique across HWUnits."/>
                          </a:da>

                          <!-- AdcLogicalChannelId -->
                          <v:var name="AdcLogicalChannelId" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>This is the logical Id of the ADC channel.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Logical Channel ID"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:e2e71142-5b64-4236-b15f-9b880ca8325b"/>
                            <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, '0')"/>
                            <a:da name="RANGE" type="XPath">
                             <a:tst expr="(. &gt;= 0) and (. &lt; num:i(count(node:current()/../../*)))"
                             false="Value out of range: must be in range 0 to N-1 (N is number of configured channels). Use the Calc button to calculate correct default value."/>
                             <a:tst expr="text:uniq(../../*/AdcLogicalChannelId, .)"
                             false="Duplicated value, AdcLogicalChannelId must be unique across all channels. Use the Calc button to calculate correct default value."/>
                            </a:da>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=1024"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelName -->
                           <v:var name="AdcChannelName" type="ENUMERATION">
                              <a:a name="DESC">
                                  <a:v><![CDATA[EN:<html><p>This parameter defines the assignment of the channel to the physical ADC hardware channel. Note: - Range of the ADC Channels depends on the selected package.
                                  </p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                              <a:a name="LABEL" value="Adc Physical Channel Name"/>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" value="ECUC:ac534dfe-22b7-4c19-a37c-12e631ff4e90"/>
                            <a:da name="DEFAULT" type="XPath" expr="node:fallback('->ecu:list(concat(&#34;Adc.AdcConfigSet.AdcHwUnit&#34;,substring-after(string(node:fallback(../../../AdcHwUnitId,&#34;ADC0&#34;)), &#34;ADC&#34;),&#34;.AdcChannel.AdcChannelName&#34;))[position()-1=node:fallback(node:current()/../@index,0)]', ecu:list('Adc.AdcConfigSet.AdcHwUnit.AdcChannel.AdcChannelName')[1])"></a:da>
                            <a:da name="RANGE" type="XPath" expr="ecu:list(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after(string(node:fallback(../../../AdcHwUnitId,'')), 'ADC'),'.AdcChannel.AdcChannelName'))"/>
                              <a:da name="INVALID" type="XPath">
                                <a:tst expr="text:uniq(../../*/AdcChannelName, .)" false="Duplicate physical channel"/>
                              </a:da>
                          </v:var>

                          <!-- AdcChannelId -->
                          <!-- ECUC_Adc_00392 -->
                           <v:var name="AdcChannelId" type="INTEGER">
                              <a:a name="DESC">
                                  <a:v><![CDATA[EN:<html><p>This parameter defines the assignment of the channel to the physical ADC hardware channel. Note: - Range of the ADC Channels depends on the selected package.
                                  IMPORTANT NOTE: This node must be in sync with 'Adc Physical Channel Name': must be equal with number specified after 'ChanNum' in the selected 'Adc Physical Channel Name'. E.g. ADC_CH_0_ChanNum38 => Channel ID must be 38. The node is required by Autosar standard. 'Adc Physical Channel Name' node is added because refers to names from ReferenceManual and is more user friendly.
                                  In Tresos configurator, after selecting a new Channel Name, the new Channel Id value can be filled in automatically by using 'Calculate Value' button.
                                  </p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                              <a:a name="LABEL" value="Adc Physical Channel ID"/>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" value="ECUC:ac534dfe-22b7-4c19-a37c-12e631ff4e9d"/>
                              <a:da name="DEFAULT" type="XPath" expr="num:i(substring-after(string(node:fallback(node:current()/../AdcChannelName, 'ChanNum0')),'ChanNum'))"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&lt;=1024"/>
                                <a:tst expr="&gt;=0"/>
                              </a:da>
                              <a:da name="RANGE" type="XPath">
                                <a:tst expr="(. != num:i(substring-after(string(node:fallback(node:current()/../AdcChannelName,'ChanNum0')), 'ChanNum')))"
                            true="'Adc Physical Channel ID' must be equal with number specified after 'ChanNum' in the selected 'Adc Physical Channel Name'. E.g. ADC_CH_00_ChanNum38 => Channel ID must be 38.
                                  In Tresos configurator, after selecting a new Channel Name, the new Channel Id value can be filled in automatically by using 'Calculate Value' button."/>
                              </a:da>
                            </v:var>

                          <!-- AdcChannelConvTime -->
                          <!-- ECUC_Adc_00011 -->
                          <v:var name="AdcChannelConvTime" type="INTEGER">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Configuration of conversion time, i.e. the time during which the analogue value is converted into digital representation, (in clock cycles) for each channel, if supported by hardware.
                                  ImplementationType: Adc_ConversionTimeType.
                                  This parameter is not used by the current implementation.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Conversion Time"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:b30df7ab-3fc5-4768-8c0f-0d9f42ed03f5"/>
                            <a:da name="DEFAULT" value="0"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="INVALID" type="Range">
                                <a:tst expr="&lt;=9223372036854775807"/>
                                <a:tst expr="&gt;=0"/>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelLimitCheck -->
                          <!-- ECUC_Adc_00453, SWS_Adc_00445 -->
                          <v:var name="AdcChannelLimitCheck" type="BOOLEAN">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Enables or disables limit checking for an ADC channel.
                                This node is EDITABLE only if "Adc Enable Limit Check" is enabled</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Limit Check"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:d920aa33-74b3-4a1b-a382-8dc589c5d726"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:da name="EDITABLE" type="XPath" expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true')"/>
                            <a:a name="INVALID" type="XPath">
                               <a:tst expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'false' and .='true')" true ="AdcChannelLimitCheck must be disabled when Adc/AdcGeneral/AdcEnableLimitCheck is false."/>
                               <a:tst expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true' and .='true' and (not(node:exists(../AdcChannelLowLimit))))" true ="if Adc/AdcGeneral/AdcEnableLimitCheck is enabled, AdcChannelLowLimit must be configured."/>
                               <a:tst expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true' and .='true' and (not(node:exists(../AdcChannelHighLimit))))" true ="if Adc/AdcGeneral/AdcEnableLimitCheck is enabled, AdcChannelHighLimit must be configured."/>
                               <a:tst expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true' and .='true' and (not(node:exists(../AdcChannelRangeSelect))))" true ="if Adc/AdcGeneral/AdcEnableLimitCheck is enabled, AdcChannelRangeSelect must be configured."/>
                            </a:a>
                          </v:var>

                          <!-- AdcChannelHighLimit -->
                          <!-- ECUC_Adc_00455 -->
                          <v:var name="AdcChannelHighLimit" type="INTEGER">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>High limit - used for limit checking. This value depends on the Adc Hw Unit resolution.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel High Limit"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:eae2a5e4-a163-429c-ab89-c6cc99dc185f"/>
                            <a:da name="DEFAULT" value="4095"/> <!--TODO: Use max resolution value from resources -->
                            <a:da name="EDITABLE" type="XPath" expr="node:exists(../AdcChannelLimitCheck) and (../AdcChannelLimitCheck = 'true') and (../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true')"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=9223372036854775807"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                            <a:tst expr="node:exists(../AdcChannelLowLimit) and (. &lt; ../AdcChannelLowLimit) and node:exists(../AdcChannelLimitCheck) and (../AdcChannelLimitCheck = 'true') and (../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true')"
                            true="Adc Channel High Limit value should be greater or equal than Adc Channel Low Limit value."/>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelLowLimit -->
                          <!-- ECUC_Adc_00454 -->
                          <v:var name="AdcChannelLowLimit" type="INTEGER">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Low limit - used for limit checking.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Low Limit"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:15489845-b911-40f8-8764-b59c522d1517"/>
                            <a:da name="DEFAULT" value="0"/>
                            <a:da name="EDITABLE" type="XPath" expr="node:exists(../AdcChannelLimitCheck) and (../AdcChannelLimitCheck = 'true') and (../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true') "/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=9223372036854775807"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                            <a:tst expr="node:exists(../AdcChannelHighLimit) and (. &gt; ../AdcChannelHighLimit) and node:exists(../AdcChannelLimitCheck) and (../AdcChannelLimitCheck = 'true') and (../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true')"
                            true="Adc Channel Low Limit value should be less or equal than Adc Channel High Limit value."/>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelRangeSelect -->
                          <!-- ECUC_Adc_00456 -->
                          <v:var name="AdcChannelRangeSelect" type="ENUMERATION">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>In case of active limit checking: defines which conversion values are taken into account related to the boarders defined with AdcChannelLowLimit and AdcChannelHighLimit.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PreCompile">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Range Select"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:2ec9efd1-1197-4d07-9465-555535de5753"/>
                            <a:da name="DEFAULT" value="ADC_RANGE_ALWAYS"/>
                            <a:da name="EDITABLE" type="XPath" expr="node:exists(../AdcChannelLimitCheck) and (../AdcChannelLimitCheck = 'true') and (../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true')"/>
                            <a:da name="RANGE">
                              <a:v>ADC_RANGE_ALWAYS</a:v>
                              <a:v>ADC_RANGE_BETWEEN</a:v>
                              <a:v>ADC_RANGE_NOT_BETWEEN</a:v>
                              <a:v>ADC_RANGE_NOT_OVER_HIGH</a:v>
                              <a:v>ADC_RANGE_NOT_UNDER_LOW</a:v>
                              <a:v>ADC_RANGE_OVER_HIGH</a:v>
                              <a:v>ADC_RANGE_UNDER_LOW</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelRefVoltsrcHigh -->
                          <!-- ECUC_Adc_00089 -->
                          <v:var name="AdcChannelRefVoltsrcHigh" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Upper reference voltage source for each channel. Enumeration literals are defined vendor specific.
                                This parameter is not used by the current implementation.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc High Reference Voltage"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:ccf639c6-9a1d-449e-aa28-a07033992bd3"/>
                            <a:da name="DEFAULT" value="UPPER_REF_VOLT_0"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="RANGE">
                              <a:v>UPPER_REF_VOLT_0</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelRefVoltsrcLow -->
                          <!-- ECUC_Adc_00023 -->
                          <v:var name="AdcChannelRefVoltsrcLow" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Lower reference voltage source for each channel. Enumeration literals are defined vendor specific.
                                This parameter is not used by the current implementation.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Low Reference Voltage"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:6d5c22b2-3211-4f3e-a723-ed7a3666b1a6"/>
                            <a:da name="DEFAULT" value="LOWER_REF_VOLT_0"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="RANGE">
                              <a:v>LOWER_REF_VOLT_0</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelResolution -->
                          <!-- ECUC_Adc_00019 -->
                          <v:var name="AdcChannelResolution" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Channel Resolution in bits of converted value.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Resolution"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:e5a8a040-e72f-4905-809e-ae56a57e4070"/>
                            <a:da name="DEFAULT" value="12"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="RANGE" type="Range">
                                <a:tst expr="&lt;=9223372036854775807"/>
                                <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=63"/>
                              <a:tst expr="&gt;=1"/>
                            </a:da>
                          </v:var>

                          <!-- AdcChannelSampTime -->
                          <!-- ECUC_Adc_00290 -->
                          <v:var name="AdcChannelSampTime" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Sampling time, i.e. the time during which the value is sampled, (in clock cycles) for each channel. This parameter is not used by the current implementation.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Channel Sampling time"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:456a92b8-c964-4f8c-b5cb-b5273e8b4725"/>
                            <a:da name="DEFAULT" value="8"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=9223372036854775807"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="INVALID" type="XPath">
                                <a:tst expr="(. &lt; 8) or (. &gt; 254)" true="Value out of range: must be in range 8 to 254"/>
                            </a:da>
                          </v:var>

                          <!-- AdcEnablePresampling -->
                          <v:var name="AdcEnablePresampling" type="BOOLEAN">
                            <a:a name="DESC">
                             <a:v><![CDATA[EN:<html><p>When true, this parameter enables the presampling phase for the selected channel.
                              The normal operation sequence on the channel: Presampling -> Sampling -> Conversion.</p></html>]]></a:v>
                            </a:a>
                           <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Enable Presampling"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:0ff4184f-90dc-439c-b464-cdccd861499d"/>
                            <a:da name="DEFAULT" value="false"/>
                          </v:var>

                          <!-- AdcEnableThresholds -->
                          <v:var name="AdcEnableThresholds" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>When true, this parameter enables the threshold detection feature for the selected channel.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Enable Threshold"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:54bf9db3-2c2c-45ba-81d0-4df31213e7ab"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:a name="INVALID" type="XPath">
                                <a:tst expr="(../../../../../../AutosarExt/AdcEnableWatchdogApi ='false') and (. = 'true')" true="Watchdog functionality must be globally enabled (Adc/AutosarExt/AdcEnableWatchdogApi) for Watchdog to be configured for an individual channel."/>
                                <a:tst expr="(not(node:exists(../AdcThresholdRegister)) and (. = 'true'))" true="A Threshold Register must be selected for this channel if AdcEnableThresholds is set to ON."/>
                                <a:tst expr="(not(node:exists(../AdcWdogNotification)) or (node:fallback(../AdcWdogNotification, 'NULL_PTR') = 'NULL_PTR')) and  (. = 'true')" true="A valid watchdog notification must be configured for this chanel if AdcEnableThresholds is set to ON."/>
                                <a:tst expr="(. = 'true') and (../../../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()/../../../AdcHwUnitId]/WdgThresholdEnable = 'false')" true="Usage of watchdog ISR must be activated from ADCHwConfiguration for the coresponding HwUnitId if AdcEnableThresholds is set to ON."/>
                            </a:a>
                          </v:var>

                          <!-- AdcThresholdRegister -->
                          <v:ref name="AdcThresholdRegister" type="REFERENCE">
                            <a:a name="DESC">
                             <a:v><![CDATA[EN:<html><p>
                              Select the threshold register which provides the values to be used for upper and lower thresholds.
                              ADCHwUnits support threshold registers from ADC_THRESHOLD_REG_0 to ADC_THRESHOLD_REG_7.
                              Note: This is an Implementation Specific Parameter.</p></html>]]>
                             </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Threshold Control Register"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="UUID" value="ECUC:b3da110e-30a7-4ec5-a782-9f8e3823e9d6"/>
                            <a:da name="REF" value="ASPathDataOfSchema:/TS_T40D11M50I0R0/Adc/AdcConfigSet/AdcHwUnit/AdcThresholdControl"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../AdcEnableThresholds = 'true'"/>
                            </a:a>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="(../AdcEnableThresholds = 'true') and (.='')" true="Select an ADC Threshold Register for the configured channel."/>
                              <a:tst expr="(../AdcEnableThresholds = 'true') and not(contains(., ../../../@name))" true="The ADC Threshold Register must be mapped on the same Hw Unit."/>
                            </a:da>
                          </v:ref>

                          <!-- @implements AdcWdogNotification_Object -->
                          <v:var name="AdcWdogNotification" type="FUNCTION-NAME">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>This function pointer is called whenever the conversion of the channel issued a watchdog interrupt.
                       The notification takes one uint8 parameter which represents the flags(low/high threshold) that triggered the interrupt.
                       In order to interpret them please use ADC_WDG_... defines.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="LABEL" value="Adc Channel Watchdog Notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:c06c3744-e56b-44e7-87e3-71f394a58183"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../AdcEnableThresholds = 'true'"/>
                            </a:a>
                            <a:a name="INVALID" type="XPath">
                            <a:tst expr="(../AdcEnableThresholds = 'true') and not(text:uniq(../../*/AdcWdogNotification,text:replace(.,'NULL_PTR','0')))" true="Duplicate AdcWdogNotification"/>
                            </a:a>
                          </v:var>
                        </v:ctr>
                      </v:lst>

                      <!-- AdcGroup Big container -->
                      <!-- SWS_Adc_00092 -->
                      <v:lst name="AdcGroup" type="MAP">
                        <a:da name="MIN" value="1"/>

                        <!-- AdcGroup -->
                        <!-- ECUC_Adc_00028, SWS_Adc_00090 -->
                        <!-- /** @implements AdcGroup_Object */ -->
                        <v:ctr name="AdcGroup" type="IDENTIFIABLE">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>This container contains the Group configuration (parameters).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="UUID" value="ECUC:0c261952-cace-45d8-946d-577e31d90187"/>
                          <a:a name="REQUIRES-INDEX" value="true"/>
                          <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"  type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                              <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="text:uniq(../../../*/AdcGroup/*/@name, @name)" false="Duplicate AdcGroupNames, Group Symbolic names must be unique across HWUnits."/>
                          </a:da>

                          <!-- AdcGroupAccessMode -->
                          <!-- ECUC_Adc_00317 -->
                          <v:var name="AdcGroupAccessMode" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Type of access mode to group conversion results.
                                ImplementationType: Adc_GroupAccessModeType.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Access Mode"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:d7a78954-1695-43b7-a592-************"/>
                            <a:da name="DEFAULT" value="ADC_ACCESS_MODE_SINGLE"/>
                            <a:da name="RANGE">
                              <a:v>ADC_ACCESS_MODE_SINGLE</a:v>
                              <a:v>ADC_ACCESS_MODE_STREAMING</a:v>
                            </a:da>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="((../AdcWithoutInterrupts='true') and (.='ADC_ACCESS_MODE_STREAMING') and (../AdcStreamingBufferMode='ADC_STREAM_BUFFER_LINEAR'))"
                              true="Streaming access mode with ADC_STREAM_BUFFER_LINEAR is not supported without interrupts."/>
                              <a:tst expr="((../AdcWithoutInterrupts='true') and (.='ADC_ACCESS_MODE_STREAMING') and (../../../AdcTransferType ='ADC_INTERRUPT'))"
                              true="Streaming access mode without interrupts is only supported with DMA."/>
                              <a:tst expr="((../AdcGroupAccessMode='ADC_ACCESS_MODE_STREAMING') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_SW') and (../AdcGroupConversionMode='ADC_CONV_MODE_ONESHOT'))"
                              true="Group cannot be configured with streaming access mode when it is Software triggered in one shot mode."/>
                              <a:tst expr="((node:fallback(../../../../../../AutosarExt/AdcOptimizeOneShotHwTriggerConversions,'false') = 'true') and (.='ADC_ACCESS_MODE_STREAMING'))"
                              true="Streaming access mode cannot be configured if AdcOptimizeOneShotHwTriggerConversions is enabled."/>
                              <a:tst expr="((node:fallback(../../../../../../AutosarExt/AdcEnableGroupStreamingResultReorder,'true') = 'false') and (.='ADC_ACCESS_MODE_STREAMING') and (../AdcWithoutInterrupts='true') and (count(../AdcGroupDefinition/*) = 1))"
                              true="DMA streaming access mode cannot be configured for groups which have selected Without Interrupts, ACCESS_MODE_STREAMING and a single channel, unless AutosarExt Adc Enable Group Streaming Results Reorder feature is enabled. However the feature can be left disabled at group level."/>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupConversionMode -->
                          <!-- ECUC_Adc_00397 -->
                          <!-- /** @implements AdcGroupConversionMode_Object */ -->
                          <v:var name="AdcGroupConversionMode" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Type of conversion mode supported by the driver.
                                ImplementationType: Adc_GroupConvModeType.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Conversion Mode"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:203d3429-e9c2-4a48-adf3-c3441ce20fa9"/>
                            <a:da name="DEFAULT" value="ADC_CONV_MODE_ONESHOT"/>
                            <a:da name="RANGE">
                              <a:v>ADC_CONV_MODE_ONESHOT</a:v>
                              <a:v>ADC_CONV_MODE_CONTINUOUS</a:v>
                            </a:da>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="((../AdcGroupTriggSrc='ADC_TRIGG_SRC_HW') and (../AdcGroupConversionMode='ADC_CONV_MODE_CONTINUOUS'))"
                              true="Group cannot be configured for continuous conversion mode when it is hardware triggered."/>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupConversionType -->
                          <v:var name="AdcGroupConversionType" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Normal or Injected conversion type.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Conversion Type"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:42e66a8f-98f6-487b-b31a-fa16f0c49731"/>
                            <a:da name="DEFAULT" value="ADC_CONV_TYPE_NORMAL"/>
                            <a:da name="RANGE">
                              <a:v>ADC_CONV_TYPE_NORMAL</a:v>
                              <a:v>ADC_CONV_TYPE_INJECTED</a:v>
                            </a:da>
                            <a:da name="INVALID" type="XPath">
                            <a:tst expr="(.='ADC_CONV_TYPE_NORMAL') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_HW') and not(contains((node:ref(../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'EXT_TRIG'))"
                                   true="If Hardware Trigger Source comes from CTU, the conversion type must be ADC_CONV_TYPE_INJECTED."/>
                            <a:tst expr="(.='ADC_CONV_TYPE_INJECTED') and ((../AdcGroupConversionMode='ADC_CONV_MODE_CONTINUOUS') or (node:exists(../AdcGroupPriority) and (../AdcGroupPriority!=255)))"
                                   true="Injected conversions can be configured only when conversion mode is ONE SHOT and the priority is the highest (255)."/>
                            <a:tst expr="(.='ADC_CONV_TYPE_INJECTED') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_SW') and (../../../../../../AutosarExt/AdcUseSoftwareInjectedGroups = 'false')"
                                   true="Software Injected conversions must be globally enabled via Adc/AutosarExt/AdcUseSoftwareInjectedGroups parameter before configuring a group as Software triggered and Injected."/>
                            <a:tst expr="(.='ADC_CONV_TYPE_INJECTED') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_HW') and (contains((node:ref(../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'AUX_EXT_TRIG'))"
                                   true="Hardware Injected conversions do not support auxiliary external trigger"/>
                            <a:tst expr="(.='ADC_CONV_TYPE_NORMAL') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_HW') and (../../../../../../AutosarExt/AdcUseHardwareNormalGroups = 'false')"
                                   true="AdcUseHardwareNormalGroups should be enabled in Adc/AutosarExt container when group is configured as hardware normal"/>
                            <a:tst expr="(.='ADC_CONV_TYPE_NORMAL') and (../AdcGroupTriggSrc='ADC_TRIGG_SRC_HW') and not(contains((node:ref(../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'EXT_TRIG'))"
                                   true="If Adc Group Hardware Trigger Source comes from CTU, the type of group must be Hardware Injected."/>
                            <a:tst expr="(. = 'ADC_CONV_TYPE_NORMAL') and (../../../AdcTransferType = 'ADC_INTERRUPT') and (../AdcWithoutInterrupts='false') and
                                         (../../../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()/../../../AdcHwUnitId]/AdcNormalInterruptEnable = 'false')" 
                                   true="Usage of Normal ISR must be activated from ADCHwConfiguration for the coresponding HwUnitId."/>
                            <a:tst expr="(. = 'ADC_CONV_TYPE_INJECTED') and (../../../AdcTransferType = 'ADC_INTERRUPT') and (../AdcWithoutInterrupts='false') and
                                         (../../../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()/../../../AdcHwUnitId]/AdcInjectedInterruptEnable = 'false')" 
                                   true="Usage of Injected ISR must be activated from ADCHwConfiguration for the coresponding HwUnitId."/>   
                            </a:da>
                            <a:da name="WARNING" type="XPath">
                            <a:tst expr="(
                                          (. = 'ADC_CONV_TYPE_INJECTED') and (../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_SW') and (../AdcWithoutInterrupts = 'false') and
                                          ../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()/../../../AdcHwUnitId]/AdcInjectedInterruptEnable = 'false'
                                         )"
                                   true="SW Injected groups are only configured in Interrupt or Without Interrupt mode when AdcTransferType = ADC_DMA. Because of low frequency usage (i.e.: low amount of data to transfer), SW Injected groups shouldn't be used with DMA to transfer data because it reduces driver performance."/>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupId -->
                          <!-- ECUC_Adc_00398, SWS_Adc_00099 -->
                          <v:var name="AdcGroupId" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Numeric ID of the group. This parameter is the symbolic name to be used on the API. This symbolic name allows accessing Channel Group data. This value will be assigned to the symbolic name derived of the AdcGroup container shortName.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Id"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                            <a:a name="UUID" value="ECUC:6895d072-6eed-4afa-915c-8f966a943fe2"/>
                            <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index,'0')"/>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(node:fallback(.,0) &gt;= 0) and (node:fallback(.,0) &lt; num:i(count(node:fallback(../../../../*/AdcGroup/*,1))))"
                              false="Value out of range: must be in range 0 to N-1 (N is number of configured groups). Use the Calc button to calculate correct default value."/>
                              <a:tst expr="text:uniq(node:fallback(../../../../*/AdcGroup/*/AdcGroupId,text:split('0') ),node:fallback(.,1))"
                              false="Duplicate AdcGroupId, GroupIds must be unique across HWUnits."/>

                            </a:da>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="../../../../../../AdcGeneral/AdcPriorityImplementation = 'ADC_PRIORITY_HW_SW' and not(node:exists(../AdcGroupPriority))"
                              true="If Adc/AdcGeneral/AdcPriorityImplementation is set as ADC_PRIORITY_HW_SW, AdcGroupPriority must be configured for this Group."/>
                            </a:da>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=1023"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupPriority -->
                          <!-- ECUC_Adc_00287, SWS_Adc_00288 -->
                          <!-- /** @implements AdcGroupPriority_Object*/ -->
                          <v:var name="AdcGroupPriority" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Priority level of the AdcGroup. This item is ignored if Adc/AdcGeneral/AdcPriorityImplementation is defined to ADC_PRIORITY_NONE.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Group Priority"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:d1888f97-c400-42ed-94cc-2a20184fba58"/>
                            <a:da name="DEFAULT" value="0"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../../../../../../AdcGeneral/AdcPriorityImplementation != 'ADC_PRIORITY_NONE'"/>
                            </a:a>
                            <!-- SWS_Adc_00312, SWS_Adc_00289 -->
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=255"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="(node:fallback(../AdcGroupConversionType, 'ADC_CONV_TYPE_NORMAL') = 'ADC_CONV_TYPE_NORMAL') and
                                           (node:fallback(../AdcGroupTriggSrc, 'ADC_TRIGG_SRC_SW') = 'ADC_TRIGG_SRC_SW') and
                                           node:exists(node:fallback(../../../AdcGroup/*[AdcGroupTriggSrc='ADC_TRIGG_SRC_HW']/AdcGroupPriority, 0)) and
                                           (node:fallback(.,0) &gt; (num:min(node:fallback(../../../AdcGroup/*[AdcGroupTriggSrc='ADC_TRIGG_SRC_HW']/AdcGroupPriority, 0))))"
                                     true="Normal SW Triggered Groups cannot have priority more than HW Triggered Groups"/>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupReplacement -->
                          <!-- ECUC_Adc_00435, SWS_Adc_00430 -->
                          <v:var name="AdcGroupReplacement" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Replacement mechanism, which is used on ADC group level, if a group conversion is interrupted by a group which has a higher priority.
                                ImplementationType: Adc_GroupReplacementType</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Group Replacement"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:46b602ba-e3c5-4e2d-b174-f0e410757002"/>
                            <a:da name="DEFAULT" value="ADC_GROUP_REPL_ABORT_RESTART"/>
                            <a:da name="READONLY" value="true"/>
                            <a:da name="RANGE">
                              <a:v>ADC_GROUP_REPL_ABORT_RESTART</a:v>
                              <a:v>ADC_GROUP_REPL_SUSPEND_RESUME</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupTriggSrc -->
                          <!-- ECUC_Adc_00399, SWS_Adc_00279, PR-MCAL-3204.adc -->
                          <v:var name="AdcGroupTriggSrc" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Type of source event that starts a group conversion. It's possible select Hw or Sw trigger.
                              In case of Hw trigger the trigger source can be from the CTU or External hardware pins of the controller.
                              In this controller only Ctu trigger source is supported which is selected by the "Adc Group Hardware Trigger Source" parameter.
                              </p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Trigger Source"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:fa329c1c-d178-41a9-b464-6cfdd2d34e10"/>
                            <a:da name="DEFAULT" value="ADC_TRIGG_SRC_SW"/>
                            <a:a name="INVALID" type="XPath">
                              <a:tst expr="((../../../../../../AdcGeneral/AdcHwTriggerApi = 'false') and (. = 'ADC_TRIGG_SRC_HW'))"
                                     true="Adc Hw Trigger API in AdcGeneral container is switched off! Cannot configure any groups in HW triggered mode!"/>
                              <a:tst expr="((../../../../../../AdcGeneral/AdcEnableStartStopGroupApi = 'false') and (. = 'ADC_TRIGG_SRC_SW'))"
                                     true="Adc_StartStopGroup API in AdcGeneral container is switched off! Cannot configure any groups in SW triggered mode!"/>
                              <a:tst expr="((node:fallback(../../../../../../AutosarExt/AdcOptimizeOneShotHwTriggerConversions,'false') = 'true') and (.='ADC_TRIGG_SRC_SW'))"
                                     true="Software triggered groups cannot be configured if Adc/AutosarExt/AdcOptimizeOneShotHwTriggerConversions is enabled."/>
                              <a:tst expr="(.='ADC_TRIGG_SRC_HW') and (count(../../../../../../AdcHwTrigger/*[AdcHwTrigSrc = 'EXT_TRIG']) = 0) and (../../../../../../AdcHwConfiguration/*[AdcHwConfiguredId = node:current()/../../../AdcHwUnitId]/CtuFifoOfInterruptEnable = 'false')" 
                                     true="In Adc Hw Configuration element Hw CTU FIFO OF ISR need to be enabled"/>
                            </a:a>
                            <a:da name="RANGE">
                              <a:v>ADC_TRIGG_SRC_HW</a:v>
                              <a:v>ADC_TRIGG_SRC_SW</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcGroupHwTriggerSource -->
                          <v:ref name="AdcGroupHwTriggerSource" type="REFERENCE">
                            <a:a name="DESC">
                             <a:v><![CDATA[EN:<html><p>
                              Select the HW trigger signal for triggering the conversion group. Configurable only when HW trigger source is selected for the group.</p></html>]]>
                             </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Group Hardware Trigger Source"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="UUID" value="ECUC:b3da110e-30a7-4ec5-a782-9f8e3853e9d6"/>
                            <a:da name="REF" value="ASPathDataOfSchema:/TS_T40D11M50I0R0/Adc/AdcConfigSet/AdcHwTrigger"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW'"/>
                            </a:a>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="(../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW') and (.='')" true="Select an Adc Hw Trigger for the configured group."/>
                            </a:da>
                          </v:ref>

                          <!-- AdcHwTrigSignal -->
                          <!-- ECUC_Adc_00400 -->
                          <v:var name="AdcHwTrigSignal" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Configures the edge of the hardware trigger signal, i.e. to start the conversion.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Group Hardware Trigger Signal"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:5104fe2c-323c-4da0-bb1f-6b77d7b47c44"/>
                            <a:da name="DEFAULT" value="ADC_HW_TRIG_RISING_EDGE"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../../AdcGeneral/AdcHwTriggerApi = 'true') and (../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW')"/>
                            </a:a>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="(.='ADC_HW_TRIG_BOTH_EDGES')" true="This platform does not support both edges at the same time."/>
                            </a:da>
                            <a:da name="RANGE">
                              <a:v>ADC_HW_TRIG_BOTH_EDGES</a:v>
                              <a:v>ADC_HW_TRIG_RISING_EDGE</a:v>
                              <a:v>ADC_HW_TRIG_FALLING_EDGE</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcHwTrigTimer -->
                          <!-- ECUC_Adc_00401 -->
                          <v:var name="AdcHwTrigTimer" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Reload value of the ADC module embedded timer.</p>
                                    <p>Note: This value is used to write to CTU Counter Compare Value.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Trigger Timer"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:07fd9ada-d244-4025-948f-de937c01b7e9"/>
                            <a:da name="DEFAULT" value="10"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../../AdcGeneral/AdcHwTriggerApi = 'true') and (../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW') and not(contains((node:refs(../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'EXT_TRIG'))"/>
                            </a:a>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=65535"/>
                              <a:tst expr="&gt;=0"/>
                            </a:da>
                          </v:var>

                          <!-- AdcNotification -->
                          <!-- ECUC_Adc_00402, SWS_Adc_00084, SWS_Adc_00085, SWS_Adc_00104 -->
                          <!-- /** @implements AdcNotification_Object */ -->
                          <v:var name="AdcNotification" type="FUNCTION-NAME">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Callback function for each group. This function pointer is called whenever the conversion of this group is completed.
                              This node is EDITABLE only if "Adc Notification Capability" is enabled</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v class="PostBuild">VariantPostBuild</icc:v>
                              <icc:v class="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="OPTIONAL" value="true"/>
                            <a:a name="LABEL" value="Adc Group Notification"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:80de0df6-83e0-40bb-b4a0-32767b43ff58"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../../../../../../AdcGeneral/AdcGrpNotifCapability = 'true'"/>
                            </a:a>
                          </v:var>

                          <!-- AdcExtraNotification -->
                          <!-- SWS_Adc_00084, SWS_Adc_00085 -->
                          <!-- /** @implements AdcExtraNotification_Object */ -->
                          <v:var name="AdcExtraNotification" type="FUNCTION-NAME">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Extra callback function for each group. This function pointer will be called at the beginning of the interrupt routine, before updating any HW registers or Group status.
                                This feature is enabled if "Adc Notification Capability" from General/Adc General Configuration is enabled.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Extra Notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:45315cb5-e0ed-4c8c-b013-4f21012be279"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(node:fallback(../../../../../../AutosarExt/AdcEnableInitialNotification,'false') = 'true')"/>
                            </a:a>
                          </v:var>

                          <v:var name="AdcDmaErrorNotification" type="FUNCTION-NAME">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Vendor specific: Dma error callback function for each group. This function pointer will be called at the end of the interrupt routine.
                                This feature is enabled if "Adc Dma Error Enable" from General/Adc General Configuration is enabled.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Dma Error Notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:a7ecf7c0-9534-44cb-8300-ee00256c3477"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(node:fallback(../../../../../../AutosarExt/AdcEnableDmaErrorNotification,'false') = 'true')"/>
                            </a:a>
                          </v:var>

                          <!-- AdcStreamingBufferMode -->
                          <!-- ECUC_Adc_00316 -->
                          <v:var name="AdcStreamingBufferMode" type="ENUMERATION">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Select the streaming buffer as linear buffer (i.e. the ADC Driver stops the conversion as soon as the stream buffer is full) or as circular buffer (wraps around if the end of the stream buffer is reached).
                                This feature is enabled only if "Adc Group Access Mode" is set to ADC_ACCESS_MODE_STREAMING.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Streaming Buffer Mode"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:cbf96f21-c6c9-4f34-9047-055111ce2269"/>
                            <a:da name="DEFAULT" value="ADC_STREAM_BUFFER_LINEAR"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING'"/>
                            </a:a>
                            <a:a name="INVALID" type="XPath">
                              <a:tst expr="(../AdcGroupAccessMode = 'ADC_ACCESS_MODE_SINGLE' and .='ADC_STREAM_BUFFER_CIRCULAR')" true ="When AdcGroupAccessMode is Single Access, the AdcStreamingBufferMode must be Linear."/>
                            </a:a>
                            <a:da name="RANGE">
                              <a:v>ADC_STREAM_BUFFER_LINEAR</a:v>
                              <a:v>ADC_STREAM_BUFFER_CIRCULAR</a:v>
                            </a:da>
                          </v:var>

                          <!-- AdcEnableOptimizeDmaStreamingGroups_Object -->
                          <v:var name="AdcEnableOptimizeDmaStreamingGroups" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>
                                Enable/Disable The Adc driver enable Optimize DMA streaming groups for reducing the number of interrupts required for processing the conversions of Adc Groups that consist of one or more channels (depending on HW capabilities) and which are configured as ADC_ACCESS_MODE_STREAMING.
                                When this feature is enabled, only one interrupt will be raised after the completion of all stream conversions (as configured by AdcStreamingNumSamples parameter). An additional interrupt to be raised after half of the stream is converted shall also be configurable.
                                This feature is enabled if "Adc Global Enable DMA Transfer" from General/AutosarExt is also enabled.</p></html>]]>
                              </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Enable Optimize DMA Streaming Groups"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:3ca7b97c-36e2-4d2a-a199-37c07804cb65"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:da name="READONLY" value="false"/>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="(.='true') and (node:fallback(../AdcGroupAccessMode, 'ADC_ACCESS_MODE_SINGLE') = 'ADC_ACCESS_MODE_SINGLE')" true="Only groups configured with ADC_ACCESS_MODE_STREAMING Access Mode can have enabled the Double Buffering feature!"/>
                              <a:tst expr="((.='true') and (node:fallback(../../../AdcTransferType, 'ADC_INTERRUPT') = 'ADC_INTERRUPT'))"
                                     true="AdcEnableOptimizeDmaStreamingGroups can be enabled only if ADC_DMA transfer is configured."/>
                              <a:tst expr="((.='true') and (node:fallback(../../../../../../AutosarExt/AdcOptimizeDmaStreamingGroups,'false') = 'false'))"
                                     true="AdcEnableOptimizeDmaStreamingGroups can be enabled only if AdcOptimizeDmaStreamingGroups is enabled."/>
                              <a:tst expr="((.='true') and (node:fallback(../AdcWithoutInterrupts,'false') = 'true'))"
                                     true="AdcEnableOptimizeDmaStreamingGroups cannot be enabled simultaneously with without interrupts."/>
                              <a:tst expr="((.='true') and (../AdcStreamResultGroup = 'false') and (count(../AdcGroupDefinition/*) > 1))"
                                     true="Adc Enable Optimize Dma Streaming Groups must be enabled together with Adc Group Streaming Results Reorder if configured channels > 1"/>
                            </a:da>
                            <a:da name="EDITABLE" type="XPath" expr="node:fallback(../../../../../../AutosarExt/AdcOptimizeDmaStreamingGroups,'false') = 'true'"/>
                          </v:var>

                          <!-- AdcEnableHalfInterrupt -->
                          <v:var name="AdcEnableHalfInterrupt" type="BOOLEAN">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>
                                          Enable/ Disable the interrupt when half sample complete for optimize DMA streaming groups feature.
                                          The "Adc Optimize DMA Streaming Groups" must be enabled for configuring this feature.</p></html>]]>
                                </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Enable Half Interrupt"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:c08c3abb-86b5-4f8f-be15-672bb9fdf90c"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:da name="READONLY" value="false"/>
                            <a:da name="EDITABLE" type="XPath" expr="../AdcEnableOptimizeDmaStreamingGroups = 'true'"/>
                            <a:da name="INVALID" type="XPath">
                                <a:tst expr="(.='true') and (node:fallback(../AdcEnableOptimizeDmaStreamingGroups, 'false') = 'false')" true="Optimize DMA streaming groups must be enabled for configuring this feature."/>
                            </a:da>
                          </v:var>

                          <!-- AdcStreamingNumSamples -->
                          <!-- ECUC_Adc_00292 -->
                          <v:var name="AdcStreamingNumSamples" type="INTEGER">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Number of ADC values to be acquired per channel in streaming access mode.
                                Note: in single access mode this parameter assumes value 1, since only one sample per channel is processed.
                                ImplementationType: Adc_StreamNumSampleType.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Streaming Number Samples"/>
                            <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:40da834d-1ca7-4ae7-b1e4-2c69f1df0bd2"/>
                            <a:da name="DEFAULT" value="1"/>
                            <a:da name="INVALID" type="Range">
                              <a:tst expr="&lt;=255"/>
                              <a:tst expr="&gt;=1"/>
                            </a:da>
                            <a:da name="RANGE" type="XPath">
                              <a:tst expr="../AdcGroupAccessMode = 'ADC_ACCESS_MODE_SINGLE' and ../AdcStreamingNumSamples != 1" true="AdcStreamingNumSamples must be 1 for ADC_ACCESS_MODE_SINGLE."/>
                            </a:da>
                          </v:var>

                          <!-- AdcStreamResultGroup -->
                          <v:var name="AdcStreamResultGroup" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>
                                 Arrange the ADC results as multiple sets of group result buffer.
                                 E.g: for a group with channels {CH1 CH5 CH7} the resulting stream buffer shall be:
                                 { CH1, CH5, CH7, CH1, CH5, CH7, CH1, CH5, CH7}
                                 instead of
                                 { CH1, CH1, CH1, CH5, CH5, CH5, CH7, CH7, CH7} like supported by AUTOSAR standard.
                                 This Parameter can be configured only for groups configured with ADC_ACCESS_MODE_STREAMING Access Mode
                              </p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Streaming Results Reorder"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:cbf96f20-c6c0-4f30-9040-055111ce2260"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="../AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING' and ../../../../../../AutosarExt/AdcEnableGroupStreamingResultReorder = 'true'"/>
                            </a:a>
                          </v:var>

                          <!-- AdcEnableChDisableChGroup -->
                          <v:var name="AdcEnableChDisableChGroup" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>
                                If this parameter is enabled, it allows the feature of enabling or disabling a particular channel in the group.<br>
                                Max.no of Groups with this feature enabled, should be configured are 254 if the configuration parameter AdcEnableChDisableChApi is enabled in Autosar Extension container.
                                <h1>Note</h1>: This is an Implementation Specific Parameter.</p></html>]]>
                              </a:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Enable/Disable channels"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:2b73e45e-a21d-4965-aef8-aeed1bd8c5bc"/>
                            <a:da name="DEFAULT" value="false"/>
                            <a:da name="EDITABLE" type="XPath" expr="../../../../../../AutosarExt/AdcEnableChDisableChApi = 'true'"/>
                            <a:da name="INVALID" type="XPath">
                              <a:tst expr="(num:i(count(../../*[AdcEnableChDisableChGroup='true'])) &gt; 254) and (../../../../../../AutosarExt/AdcEnableChDisableChApi = 'true')" true="Out of range. The maximum number of Adc Groups which can have AdcEnableChDisableChGroup feature enabled are 254."/>
                            </a:da>
                          </v:var>

                          <!-- CPR_RTD_00048.adc, CPR_RTD_00488.adc -->
                          <!-- AdcWithoutInterrupts -->
                          <!-- /** @implements AdcWithoutInterrupts_Object */ -->
                          <v:var name="AdcWithoutInterrupts" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>
                                  Enable/ Disable the occurring of ADC Interrupts and Reading of the group conversion results periodically without interrupts (ADC interrupt or DMA notification).
                                  <ul>
                                      <li>1. When this parameter is enabled, interrupts are disabled. The conversion will run without software intervention (no interrupt generated anymore) and the application can read the results by calling Adc_ReadGroup().</li>
                                      <li>2. If Transfer Type is ADC_INTERRUPT (or ADC_DMA) and this one is enabled, the result buffer registered via Adc_SetupResultBuffer will no longer be used populated with results. Adc_ReadGroup will read the results directly from ADC HW registers (or Internal Dma Buffer).</li>
                                  </ul>
                                  When this parameter is Disabled, normal functionality shall be executed.
                                  Note: This is an Implementation Specific Parameter.</p></html>]]>
                              </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="LABEL" value="Adc Group Without Interrupts"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:8ae95a2e-20c3-4904-8d11-a56b32f55761"/>
                            <a:da name="DEFAULT" value="false"/>
                          </v:var>
                        <!-- CPR_RTD_00489.adc -->
                        <!-- /** @implements AdcWithoutDma_Object */ -->
                        <v:var name="AdcWithoutDma" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>
                                          When true, disables completely DMA configuration done by ADC driver for the group, to not affect other groups for which the ADC driver has already configured the DMA. It is intended to be used when DMA Transfer Mode is selected for the ADC unit, for groups required to work without interrupt and without DMA.
                                        </p></html>]]>
                            </a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:da name="INVALID" type="XPath">
                              <a:tst expr="(. = 'true') and (../../../AdcTransferType = 'ADC_INTERRUPT')" true="This feature can be used only when ADC Unit Transfer Type = ADC_DMA"/>
                              <a:tst expr="(. = 'true') and (../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW')" true="This feature can be used only when Group Trigger Source = SW"/>
                              <a:tst expr="(. = 'true') and (../AdcWithoutInterrupts = 'false')" true="This feature can be used only together with Group Without Interrupts enabled"/>
                              <a:tst expr="(. = 'true') and (../AdcGroupAccessMode = 'ADC_ACCESS_MODE_STREAMING')" true="This feature can be used only with Oneshot, single access mode"/>
                          </a:da>
                          <a:da name="EDITABLE" type="XPath" expr="../../../AdcTransferType = 'ADC_DMA'"/>
                          <a:a name="LABEL" value="Adc Group Without Dma"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:47b4113a-9144-11eb-a8b3-0242ac130003"/>
                          <a:da name="DEFAULT" value="false"/>
                          </v:var>

                          <!-- CPR_RTD_00489.adc -->
                          <!-- /** @implements AdcExtDMAChanEnable_Object */ -->
                          <v:var name="AdcExtDMAChanEnable" type="BOOLEAN">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>
                                            Enable/ Disable DMA functionality of individual group.
                                            In this mode the adc will only enable the dma request for the last channel in the group and, so that the dma transfer will start after the group conversion has finished.
                                            In this mode it is the users responsibility to configure the dma to transfer the data according to his needs.
                                          </p></html>]]>
                              </a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:da name="INVALID" type="XPath">
                                <a:tst expr="(. = 'true') and (../../../AdcTransferType = 'ADC_INTERRUPT')" true="This feature is enabled when AdcTransferType = ADC_DMA"/>
                            </a:da>
                            <a:a name="LABEL" value="Adc External Dma Chan Config"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:2dbb0804-e88c-4f64-8bf9-b70ce22e5b50"/>
                            <a:da name="DEFAULT" value="false"/>
                           </v:var>

                          <!-- AdcGroupDefinition -->
                          <!-- ECUC_Adc_00014 -->
                          <!-- /** @implements AdcGroupDefinition_Object */ -->
                          <v:lst name="AdcGroupDefinition">
                            <!-- SWS_Adc_00091 -->
                            <a:da name="MIN" value="1"/>
                            <a:da name="INVALID" type="XPath">
                              <!-- SWS_Adc_00451 -->
                              <a:tst expr="(../../../../../../AdcGeneral/AdcEnableLimitCheck = 'true') and (num:i(count(node:current()/*)) &gt; 1) and (num:i(count(node:refs(./*)/AdcChannelLimitCheck[.='true'])) > 0)"
                                true="ADC Channel group must contain exactly one ADC Channel if the global limit checking feature is enabled and a channel with specific limit checking is into the group."/>
                              <a:tst expr="((num:i(count(node:current()/*)) &gt; num:i(ecu:get('AdcMaxCTUFifoDepth'))) and (../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW') and not(contains((node:refs(../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'EXT_TRIG')))"
                                true="If trigger source comes from CTU, number of channels in Group is not greater than FIFO_DEPTH."/>
                            </a:da>
                            <!-- /** @requirements SWS_Adc_00098 */ -->
                            <v:ref name="AdcGroupDefinition" type="REFERENCE">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Assignment of channels to a AdcGroups. For each AdcChannel that should belong to the group, a reference needs to be defined.</p></html>]]></a:v>
                              </a:a>
                              <a:a name="REQUIRES-INDEX" value="true"/>
                              <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v class="PostBuild">VariantPostBuild</icc:v>
                                <icc:v class="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="UUID" value="ECUC:76184de4-946a-49fe-835f-148b1b80f3f5"/>
                              <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Adc/AdcConfigSet/AdcHwUnit/AdcChannel"/>
                              <a:da name="INVALID" type="XPath">
                                <a:tst expr=".=''" true="Select an ADC channel for the configured group."/>
                                <!-- /** @requirements SWS_Adc_00277 */ -->
                                <a:tst expr="contains(., ../../../../@name)" false="The ADC channel must be mapped on the same Hw Unit Group."/>
                                <a:tst expr="count(text:grep(../../../../AdcChannel/*/@name, substring-after(substring-after(substring-after(substring-after(substring-after(string(.), '/'), '/'),'/'),'/'), '/')))>0"
                                    false="The ADC channel must be defined on the Hw Unit Group."/>
                                <a:tst expr="text:uniq(../*, .)" false="Duplicate ADC channels"/>
                                <a:tst expr="(../../AdcGroupTriggSrc = 'ADC_TRIGG_SRC_HW') and not(contains((node:refs(../../AdcGroupHwTriggerSource)/AdcHwTrigSrc), 'EXT_TRIG')) and ((num:i(text:split(node:refs(.)/AdcChannelName, 'ChanNum')[last()])) > 15)"
                                    true="When the group hardware has trigger source that comes from CTU, the Channel ID can not greater than 15 because CLR_A_x.CH where ID will be written have 4 bits."/>
                              </a:da>
                            </v:ref>
                          </v:lst>

                          <!-- This node is required by Autosar but set editable false because not supported by driver implementation -->
                          <!-- ECUC_Adc_00465 -->
                          <!--  @implements AdcGroupEcucPartitionRef_Object  -->
                          <v:lst name="AdcGroupEcucPartitionRef">
                            <a:a name="EDITABLE" value="false"/>
                            <v:ref name="AdcGroupEcucPartitionRef"
                                    type="REFERENCE">
                              <a:a name="DESC">
                                <a:v>Maps an ADC channel group to zero or multiple ECUC partitions to limit the access to this channel group. The ECUC partitions referenced are a subset of the ECUC partitions where the ADC driver is mapped to.</a:v>
                                <a:v>Tags: atp.Status=draft</a:v>
                              </a:a>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                    type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                                <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                              <a:a name="POSTBUILDVARIANTMULTIPLICITY"
                                    value="true"/>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="ECU"/>
                              <a:a name="UUID"
                                    value="ECUC:39370532-a5e1-4b76-bb7a-609547382d1d"/>
                              <a:da name="REF"
                                    value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                            </v:ref>
                          </v:lst>

                          <!-- AdcGroupConversionConfiguration Big Container -->
                          <v:ctr name="AdcGroupConversionConfiguration" type="IDENTIFIABLE">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Configure the Sampling and Conversion TimeGroup.
                                Note: This is an Implementation Specific Parameter</p></html>]]></a:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:d77618f8-2d5a-4a37-a4bd-170c2e38ed65"/>

                            <!-- AdcSamplingDuration0 -->
                            <v:var name="AdcSamplingDuration0" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Select the Sampling Duration for channels 0-31 from CTR0 register</p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="LABEL" value="Adc Group Sampling Duration 0"/>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../../../AutosarExt/AdcConvTimeOnce = 'false')"/>
                              </a:a>
                              <a:a name="UUID" value="ECUC:501963c5-cc32-439f-b0e1-8ccc2ecf4d82"/>
                              <a:da name="DEFAULT" value="22"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=22"/>
                                <a:tst expr="&lt;=255"/>
                              </a:da>
                            </v:var>

                            <!-- AdcSamplingDuration1 -->
                            <v:var name="AdcSamplingDuration1" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Select the Sampling Duration for channels 32-63 from CTR1 register</p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="LABEL" value="Adc Group Sampling Duration 1"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(../../../../../../../AutosarExt/AdcConvTimeOnce = 'false')"/>
                              </a:a>
                              <a:a name="UUID" value="ECUC:eab7965f-c040-4b80-bbd5-d285fefc9b2c"/>
                              <a:da name="DEFAULT" value="22"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=22"/>
                                <a:tst expr="&lt;=255"/>
                              </a:da>
                            </v:var>
                          </v:ctr> <!-- End of AdcGroupConversionConfiguration Big Container -->

                          <!-- AdcAlternateGroupConvTimings Big Container -->
                          <v:ctr name="AdcAlternateGroupConvTimings" type="IDENTIFIABLE">
                            <a:a name ="DESC">
                              <a:v>
                              <![CDATA[<html><p>Selects Alternate values used in Adc_SetClockMode API for programming CTR Conversion Timing Registers.
                              This container is EDITABLE only if "Adc Conversion Time Once" is disabled and "Adc Set Clock Mode API" is enabled. Hardware averaging functionality is also available for configuration if supported.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:0cb2283c-677d-434e-80a6-169b5c6e63e4"/>

                            <!-- AdcAltGroupSamplingDuration0 -->
                            <v:var name="AdcAltGroupSamplingDuration0" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Select the Alternate Sampling Duration for channels 0-31 from CTR0 register</p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="LABEL" value="Adc Group Alternate Sampling Duration 0"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(node:fallback(../../../../../../../AutosarExt/AdcEnableDualClockMode,'false') = 'true')
                                           and
                                           (node:fallback(../../../../../../../AutosarExt/AdcConvTimeOnce,'false')) = 'false'"/>
                              </a:a>
                              <a:a name="UUID" value="ECUC:16426534-25ba-4d05-a8f6-30f6879cb9d3"/>
                              <a:da name="DEFAULT" value="22"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=22"/>
                                <a:tst expr="&lt;=255"/>
                              </a:da>
                            </v:var>

                            <!-- AdcAltGroupSamplingDuration1 -->
                            <v:var name="AdcAltGroupSamplingDuration1" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Select the Alternate Sampling Duration for channels 32-63 from CTR1 register</p></html>]]></a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="LABEL" value="Adc Group Alternate Sampling Duration 1"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="EDITABLE" type="XPath">
                              <a:tst expr="(node:fallback(../../../../../../../AutosarExt/AdcEnableDualClockMode,'false') = 'true')
                                           and
                                           (node:fallback(../../../../../../../AutosarExt/AdcConvTimeOnce,'false')) = 'false'"/>
                              </a:a>
                              <a:a name="UUID" value="ECUC:2c6d2b3b-ce1b-49cc-95cb-4362f99f92d2"/>
                              <a:da name="DEFAULT" value="22"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=22"/>
                                <a:tst expr="&lt;=255"/>
                              </a:da>
                            </v:var>
                          </v:ctr> <!-- End of AdcAlternateGroupConvTimings Big Container -->

                        </v:ctr>
                      </v:lst>

                      <v:lst name="AdcThresholdControl" type="MAP">
                        <a:da name="MIN" value="0"/>
                        <a:da name="MAX" value="8"/>
                        <a:a name="EDITABLE" type="XPath">
                            <a:tst expr="../AdcChannel/*/AdcEnableThresholds ='true'"/>
                        </a:a>
                        <a:a name="INVALID" type="XPath">
                            <a:tst expr="(../AdcChannel/*/AdcEnableThresholds ='true') and (count(node:current()/*) &lt; 1)" true="At least one Threshold register must be configured."/>
                        </a:a>
                          <!-- AdcThresholdControl Big Container -->
                          <v:ctr name="AdcThresholdControl" type="IDENTIFIABLE">
                            <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Configure threshold detection feature for the selected channel.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="REQUIRES-INDEX" value="true"/>
                            <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                 type="IMPLEMENTATIONCONFIGCLASS">
                              <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                              <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="UUID" value="ECUC:fe3316cb-3665-458f-98cd-7b4edfd71ff6"/>
                            <a:a name="INVALID" type="XPath">
                                <a:tst expr="text:uniq(../../../*/AdcThresholdControl/*/@name, @name)" false="Duplicate AdcThresholdControl names, AdcThresholdControl Symbolic names must be unique across HWUnits."/>
                            </a:a>
                            <!-- AdcThresholdControlRegister -->
                            <v:var name="AdcThresholdControlRegister" type="ENUMERATION">
                              <a:a name="DESC">
                               <a:v><![CDATA[EN:<html><p>
                                Select the threshold register which provides the values to be used for upper and lower thresholds.
                                ADCHwUnits support threshold registers from ADC_THRESHOLD_REG_0 to ADC_THRESHOLD_REG_7.
                                Note: This is an Implementation Specific Parameter.</p></html>]]>
                               </a:v>
                              </a:a>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="LABEL" value="Adc Threshold Register"/>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" value="ECUC:b1b40439-ce34-459d-a807-42efc322c929"/>
                              <a:da name="DEFAULT" value="ADC_THRESHOLD_REG_0"/>
                              <a:da name="RANGE">
                               <a:v>ADC_THRESHOLD_REG_0</a:v>
                               <a:v>ADC_THRESHOLD_REG_1</a:v>
                               <a:v>ADC_THRESHOLD_REG_2</a:v>
                               <a:v>ADC_THRESHOLD_REG_3</a:v>
                               <a:v>ADC_THRESHOLD_REG_4</a:v>
                               <a:v>ADC_THRESHOLD_REG_5</a:v>
                               <a:v>ADC_THRESHOLD_REG_6</a:v>
                               <a:v>ADC_THRESHOLD_REG_7</a:v>
                              </a:da>
                              <a:da name="INVALID" type="XPath">
                                <a:tst expr="text:uniq(../../*/AdcThresholdControlRegister, .)" false="Duplicate Threshold register."/>
                                <a:tst expr="node:containsValue( ecu:list(concat('Adc.AdcConfigSet.AdcHwUnit',substring-after(node:fallback(../../../AdcHwUnitId, 'ADC0'), 'ADC'),'.AdcThrhlrRegisters')), concat('ADC_THRHLR', substring-after(string(.), 'ADC_THRESHOLD_REG_')))"
                                 false="Threshold register is not available for this unit"/>
                              </a:da>
                            </v:var>

                            <!-- AdcHighThreshold -->
                            <v:var name="AdcHighThreshold" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Set the value for High Threshold. This value depends on the Adc Hw Unit resolution.</p></html>]]></a:v>
                              </a:a>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="LABEL" value="Adc High Threshold value"/>
                              <a:a name="OPTIONAL" value="true"/>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" value="ECUC:3a8e1511-ecfc-4d62-b6ac-4b906976508d"/>
                              <a:da name="DEFAULT" value="4095"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=0"/>
                                <a:tst expr="&lt;=4095"/>
                              </a:da>
                              <a:da name="RANGE" type="XPath">
                                <a:tst expr="(. &gt; 4095)"
                                true="Maximum threshold value should not be greater than 4095. ( 12 bit resolution )"/>
                                <a:tst expr="(. &lt; node:fallback(../AdcLowThreshold, 0))"
                                true="High threshold value cannot be lower than low threshold value."/>
                              </a:da>
                            </v:var>

                            <!-- AdcLowThreshold -->
                            <v:var name="AdcLowThreshold" type="INTEGER">
                              <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>Set the value for Low Threshold.</p></html>]]></a:v>
                              </a:a>
                              <a:a name="SCOPE" value="LOCAL"/>
                              <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                              <a:a name="IMPLEMENTATIONCONFIGCLASS"
                                   type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                              </a:a>
                              <a:a name="LABEL" value="Adc Low Threshold value"/>
                              <a:a name="OPTIONAL" value="true"/>
                              <a:a name="ORIGIN" value="NXP"/>
                              <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                              <a:a name="UUID" value="ECUC:8b31c098-4a54-4ac6-821a-9de2e6856c55"/>
                              <a:da name="DEFAULT" value="0"/>
                              <a:da name="INVALID" type="Range">
                                <a:tst expr="&gt;=0"/>
                                <a:tst expr="&lt;=4095"/>
                              </a:da>
                              <a:da name="RANGE" type="XPath">
                               <a:tst expr="(. &gt; 4095)"
                               true="Low threshold value should not be greater than 4095. ( 12 bit resolution )"/>
                               <a:tst expr="(. &gt; node:fallback(../AdcHighThreshold, 4095))"
                               true="Low threshold value cannot be greater than high threshold value."/>
                               </a:da>
                            </v:var>
                          </v:ctr>
                      </v:lst>

                      <v:lst name="AdcHwUnitEcucPartitionRef">
                        <a:da name="INVALID" type="XPath">
                            <a:tst expr="num:i(count(node:current()/*)) &gt;1"
                            true="Each HwUnit can only have one partition."/>
                            <a:tst expr="((num:i(count(../../../../AdcGeneral/AdcEcucPartitionRef/*)) > 0) and (num:i(count(./*)) = 0))"
                            true="When AdcMulticoreSupport is enabled, The ECUC Partition for HWUnits must be configured."/>
                        </a:da>
                        <a:a name="EDITABLE" type="XPath">
                            <a:tst expr="../../../../AutosarExt/AdcMulticoreSupport ='true'"/>
                        </a:a>
                      <v:ref name="AdcHwUnitEcucPartitionRef" type="REFERENCE">
                        <a:a name="DESC">
                          <a:v>Maps a ADC hardware unit to zero or one ECUC partition to limit the access to this hardware unit. The ECUC partitions referenced are a subset of the ECUC partitions where the ADC driver is mapped to.</a:v>
                          <a:v>Tags: atp.Status=draft</a:v>
                          <a:v>This node is EDITABLE only if "Adc Multicore Support" is enabled</a:v>
                        </a:a>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                          <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="ECU"/>
                        <a:a name="UUID"
                             value="ECUC:14cc6e73-619e-4029-a793-3535990384ae"/>
                        <a:da name="REF"
                              value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                        <a:da name="INVALID" type="XPath">
                            <a:tst expr="count(text:grep(../../../../../AdcGeneral/AdcEcucPartitionRef/*, .))>0"
                               false="The ECUC partition must be defined on the AdcEcucPartitionRef."/>
                        </a:da>
                      </v:ref>
                      </v:lst>
                    </v:ctr>
                  </v:lst>

                <!-- AdcHwTrigger Big container -->
                <v:lst name="AdcHwTrigger" type="MAP">
                <a:da name="MIN" value="0"/>
                <a:da name="EDITABLE" value="true"/>
                <!-- <a:da name="EDITABLE" type="XPath" expr="count(../AdcHwUnit/*/AdcGroup/*[AdcGroupTriggSrc='ADC_TRIGG_SRC_HW']) > 0"/> Editable only if at least one group has HW trigger source selected. -->
                <a:da name="INVALID" type="XPath">
                  <a:tst expr="(../../AdcGeneral/AdcHwTriggerApi ='true') and (count(./*) &lt; 1)"
                        true="There must be at least one hardware trigger configured when using Hardware Trigger API."/>
                </a:da>
                <v:ctr name="AdcHwTrigger" type="IDENTIFIABLE">
                    <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>This container contains the Hardware trigger source configured for the group. Editable only if at least one group has HW trigger source selected. </p></html>]]></a:v>
                    </a:a>
                    <a:a name="REQUIRES-INDEX" value="true"/>
                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS"
                        type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="UUID" value="ECUC:1df7a136-db83-44a5-ac85-ff21c8db3f05"/>

                    <!-- AdcHwTrigSrc -->
                    <!--  @implements AdcHwTrigSrc_Object  -->
                    <v:var name="AdcHwTrigSrc" type="ENUMERATION">
                    <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>
                        On this implementation the HW triggers available are from Ctu.
                        Note: This is an implementation specific parameter.</p></html>]]>
                        </a:v>
                    </a:a>
                    <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS"
                        type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="LABEL" value="Adc Group Hardware Trigger Source"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:c9e2c854-915c-43a8-a537-c90d7f023a71"/>
                    <a:da name="DEFAULT" value="CTU_PWM_REL_FTM0_CH0"/>
                    <a:a name="INVALID" type="XPath">
                    <a:tst expr="text:uniq(../../*/AdcHwTrigSrc, .)" false="Duplicate trigger source for ADC Group."/>
                    </a:a>
                    <a:da name="RANGE" type="XPath" expr="ecu:list(node:when(boolean (ecu:get('Adc.ERR050473') = 'TRUE'),string('Adc.AdcConfigSet.AdcHwTrigSrc.ERR050473'),string('Adc.AdcConfigSet.AdcHwTrigSrc')))"/>
                    </v:var>
                </v:ctr>
                </v:lst>

                <!-- CtuHwUnit List -->
                <v:lst name="CtuHwUnit" type="MAP">
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="num:i(count(node:current()/*)) &gt; ecu:get(&apos;Adc.AdcConfigSet.CtuHwUnit&apos;)"
                          true="Maximum CTU Unit available for the selected derivative was exceeded."/>
                    <a:tst expr="(../../AutosarExt/AdcEnableCtuControlModeApi ='true') and (num:i(count(node:current()/*)) &lt; 1)"
                          true="At least one CTU unit must be configured when enable CTU Control Mode."/>
                  </a:da>
                  <a:a name="EDITABLE" type="XPath">
                    <a:tst expr="../../AutosarExt/AdcEnableCtuControlModeApi ='true'"/>
                  </a:a>
                  <!-- CtuHwUnit Big container -->
                  <v:ctr name="CtuHwUnit" type="IDENTIFIABLE">
                    <a:a name="DESC">
                      <a:v><![CDATA[EN:<html>This container contains configuration of the Ctu unit. This node is EDITABLE only if "Adc CTU Control Mode API" is enabled.</html>]]></a:v>
                    </a:a>
                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                      <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="UUID" value="ECUC:84be99a2-d44b-482a-8de4-f4566c470dc9"/>

                    <!-- General Tabs -->
                    <!-- Ctu Physical Id -->
                    <v:var name="CtuHwUnitId" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Specifies the used CTU Hardware Unit.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="LABEL" value="Ctu Hardware Unit"/>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:f5a19750-d0fa-4cd5-a6db-723a815e1941"/>
                      <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index,'0')"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=0"/>
                        <a:tst expr="&lt;=0"/>
                      </a:da>
                      <a:da name="RANGE" type="XPath">
                        <a:tst expr="text:uniq(node:fallback(../../*/CtuHwUnitId, text:split('0')), node:fallback(., 1))"
                            false="Duplicate Ctu Hw Unit id. Use the Calc button to calculate correct default value."/>
                        <a:tst expr="node:fallback(.,0) &gt;= ecu:get(&apos;Adc.AdcConfigSet.CtuHwUnit&apos;)"
                            true="Invalid Ctu Hw unit values. Valid values for this field are: 0. Use the Calc button to calculate correct default value."/>
                      </a:da>
                    </v:var>

                    <!-- Ctu Logical Id -->
                    <v:var name="CtuLogicalUnitId" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Specifies the Logical id of the Ctu Hardware Unit.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="LABEL" value="Ctu Logical Unit Id"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:f8f69c4d-d49e-4c7f-8651-5f7cfc5e3533"/>
                      <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, '0')"/>
                      <a:da name="RANGE" type="XPath">
                        <a:tst expr="(. &gt;= 0) and (. &lt; num:i(count(node:current()/../../*)))"
                        false="Value out of range: must be in range 0 to N-1 (N is number of configured units). Use the Calc button to calculate correct default value."/>
                        <a:tst expr="text:uniq(../../*/CtuLogicalUnitId, .)"
                        false="Duplicated value, CtuLogicalUnitId must be unique across all units. Use the Calc button to calculate correct default value."/>
                      </a:da>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;1"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>

                    <!-- Ctu Trigger Generator Mode -->
                    <v:var name="CtuTGSMode" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Select mode for Trigger Generator Subunit - double-buffered (TGSCR.TGS_M).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="LABEL" value="Ctu Trigger Generator Mode"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:b0b8ac0e-dc1d-4661-b49f-6b03c25dd9ce"/>
                      <a:da name="DEFAULT" value="TGS_MODE_TRIGGERED"/>
                      <a:da name="RANGE">
                        <a:v>TGS_MODE_TRIGGERED</a:v>
                        <a:v>TGS_MODE_SEQUENTIAL</a:v>
                      </a:da>
                      <a:da name="INVALID" type="XPath">
                        <!-- In Control Mode, we have to use TRIGGERED MODE only -->
                      </a:da>
                    </v:var>

                    <!-- Ctu Input Clock Prescaler -->
                    <v:var name="CtuInputClockPrescaler" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Input clock prescaler - double-buffered (TGSCR.PRES).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="LABEL" value="Ctu Input Clock Prescaler"/>
                      <a:a name="UUID" value="ECUC:54ecdaf1-283e-48c1-b226-9fa7049ccf95"/>
                      <a:da name="DEFAULT" value="PRESCALER_1"/>
                      <a:da name="RANGE">
                        <a:v>PRESCALER_1</a:v>
                        <a:v>PRESCALER_2</a:v>
                        <a:v>PRESCALER_3</a:v>
                        <a:v>PRESCALER_4</a:v>
                      </a:da>
                    </v:var>

                    <!-- Ctu External Trigger Mode -->
                    <v:var name="CtuExtTrigMode" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Trigger mode for external triggers: eTimers and EXT_TRG - double-buffered (TGSCR.ET_TM).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="LABEL" value="Ctu External Trigger mode"/>
                      <a:a name="UUID" value="ECUC:29eb0245-72da-4c7b-81c1-b45f41f83c10"/>
                      <a:da name="DEFAULT" value="EXT_TRIG_MODE_PULSE"/>
                      <a:da name="RANGE">
                        <a:v>EXT_TRIG_MODE_PULSE</a:v>
                        <a:v>EXT_TRIG_MODE_TOGGLE</a:v>
                      </a:da>
                    </v:var>

                    <!-- Ctu TGS Counter Compare Register -->
                    <v:var name="CtuTgsCounterCompareVal" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>TGS Counter Compare Value - double-buffered (TGSCCR).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="TGS Counter Compare Register Value"/>
                      <a:a name="UUID" value="ECUC:906fafb9-81ea-42c9-bddf-3afc5aecc64a" />
                      <a:da name="DEFAULT" value="256" />
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535" />
                        <a:tst expr="&gt;=0" />
                      </a:da>
                    </v:var>

                    <!-- Ctu TGS Counter Reload Register -->
                    <v:var name="CtuTgsCounterReloadVal" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>TGS Counter Reload Value - double-buffered (TGSCRR).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="TGS Counter Reload Register Value"/>
                      <a:a name="UUID" value="ECUC:c09d01fd-0d90-4887-996e-639cf2c6579e" />
                      <a:da name="DEFAULT" value="0" />
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=65535" />
                        <a:tst expr="&gt;=0" />
                      </a:da>
                    </v:var>

                    <!-- Ctu ADC Command List Mode -->
                    <v:var name="CtuAdcCmdListMode" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Select mode of execution for ADC Command List (LISTCSR.PAR_LIST).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="LABEL" value="ADC Command List Mode"/>
                      <a:a name="UUID" value="ECUC:8a3d4193-ea88-46f4-981d-66634edcb3bd"/>
                      <a:da name="DEFAULT" value="ADC_CMD_LIST_MODE_STREAMING"/>
                      <a:da name="RANGE">
                        <a:v>ADC_CMD_LIST_MODE_STREAMING</a:v>
                        <a:v>ADC_CMD_LIST_MODE_PARALLEL</a:v>
                      </a:da>
                    </v:var>

                    <!-- Ctu Select Input Triggers in Sequential Mode -->
                    <v:var name="CtuSeqModeMrsInput" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Select which input to be used for MRS when Sequential Mode is enabled (see CtuTGSMode) - double-buffered (TGSCR.MRS_SM)</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="LABEL" value="Select input to be used for MRS"/>
                      <a:a name="UUID" value="ECUC:d2dadbae-ff54-42e5-95e9-319a069b743e"/>
                      <a:da name="DEFAULT" type="XPath" expr="ecu:list(node:when(boolean (ecu:get('Adc.ERR050473') = 'TRUE'),string('Adc.AdcConfigSet.CtuHwUnit.CtuTrigSrc.ERR050473'),string('Adc.AdcConfigSet.CtuHwUnit.CtuTrigSrc')))[1]"/>
                      <a:a name="EDITABLE" type="XPath">
                        <a:tst expr="(../CtuTGSMode = 'TGS_MODE_SEQUENTIAL')"/>
                      </a:a>
                      <a:da name="RANGE" type="XPath" expr="ecu:list(node:when(boolean (ecu:get('Adc.ERR050473') = 'TRUE'),string('Adc.AdcConfigSet.CtuHwUnit.CtuTrigSrc.ERR050473'),string('Adc.AdcConfigSet.CtuHwUnit.CtuTrigSrc')))"/>
                    </v:var>

                    <!-- Ctu Select Input Trigger Edge in Sequential Mode -->
                    <v:var name="CtuSeqModeMrsInputEdge" type="ENUMERATION">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Select which edge of the input signal to be used for MRS when SequenceMode is enabled - double-buffered (TGSCR.MRS_SM)</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="LABEL" value="Select edge of the input signal"/>
                      <a:a name="UUID" value="ECUC:3701aa44-5be1-41c1-a1d3-0d1844dd27c2"/>
                      <a:da name="DEFAULT" value="EDGE_BOTH"/>
                      <a:a name="EDITABLE" type="XPath">
                        <a:tst expr="(../CtuTGSMode = 'TGS_MODE_SEQUENTIAL')"/>
                      </a:a>
                      <a:da name="RANGE">
                        <a:v>EDGE_RISING</a:v>
                        <a:v>EDGE_FALLING</a:v>
                        <a:v>EDGE_BOTH</a:v>
                      </a:da>
                    </v:var>

                    <!-- Ctu DMA done GRE enable -->
                    <v:var name="CtuDmaDoneGRE" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Enable setting of General Reload Enable (GRE) when DMA-done occurs (IR.DMA_DE).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="DMA done GRE enable"/>
                      <a:a name="UUID" value="ECUC:74b3f816-f73e-4521-a118-acbb73e161ce" />
                      <a:da name="DEFAULT" value="false" />
                    </v:var>

                    <!-- Ctu MRS DMA request enabled -->
                    <v:var name="CtuDmaReqMRS" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Enable issuing of DMA request when MRS occurs, if GRE is enabled (IR.MRS_DMAE).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="MRS DMA request enabled"/>
                      <a:a name="UUID" value="ECUC:4978866b-cc35-4939-8a71-b13c289c7521" />
                      <a:da name="DEFAULT" value="false" />
                    </v:var>

                    <!-- Ctu Fifo Dma Transfer Raw Data -->
                    <v:var name="CtuFifoDmaRawData" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Enable Dma Transfer Raw Data of Fifo Result Register (includes ADC Port, Channel Number and Data Conversion)</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="Fifo Dma Raw Data"/>
                      <a:a name="UUID" value="ECUC:f190ebc4-f9b5-4d58-9a8f-38f6e7e75f8a"/>
                      <a:da name="DEFAULT" value="false" />
                      <!--a:a name="EDITABLE" type="XPath" expr="../CtuFifoDmaEn/.='true'"/-->
                    </v:var>

                    <!-- Ctu Disable output -->
                    <v:var name="CtuDisableOutput" type="BOOLEAN">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>True/false - disable/enable the CTU outputs: pulses to other peripherals/pins, ADC commands or command streams.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                            type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="Disable output"/>
                      <a:a name="UUID" value="ECUC:041a6af2-e2ef-4580-a14f-f011cb11aa62" />
                      <a:da name="DEFAULT" value="false" />
                    </v:var>

                    <!-- Ctu Error Notification -->
                    <v:var name="CtuErrorNotif" type="FUNCTION-NAME">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Error callback with triggered mask error(uint32) as parameter, can check with CTU_IP_ERROR_FLAG defines.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="LABEL" value="Error Notification"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:4d752c08-7f5f-495a-8877-5b5a549a5d38"/>
                      <a:da name="DEFAULT" value="NULL_PTR"/>
                    </v:var>

                    <!-- Ctu MRS notification -->
                    <v:var name="CtuMrsNotif" type="FUNCTION-NAME">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Callback for MRS.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="LABEL" value="MRS Notification"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:2d2eb489-b1f7-4b0e-b81c-52970d0a2561"/>
                      <a:da name="DEFAULT" value="NULL_PTR"/>
                    </v:var>

                    <!-- Ctu Adc Command Issue Notification -->
                    <v:var name="CtuAdcCmdIssueNotif" type="FUNCTION-NAME">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Notification called when issuing a command to the Adc unit.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP"/>
                      <a:a name="LABEL" value="Adc Command Issue Notification"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:a0764ca5-c03d-4e65-a820-4f1cd3ba05e6"/>
                      <a:da name="DEFAULT" value="NULL_PTR"/>
                    </v:var>

                    <!-- Ctu Digital filter value -->
                    <v:var name="CtuDigitalFilter" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Digital filter for external input signal (EXT_IN).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="Digital filter value"/>
                      <a:a name="UUID" value="ECUC:45eed270-1634-43e7-854b-8460fee21722" />
                      <a:da name="DEFAULT" value="0" />
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255" />
                        <a:tst expr="&gt;=0" />
                      </a:da>
                    </v:var>

                    <!-- Ctu Control ON-Time and Guard Time for external trigger -->
                    <v:var name="CtuControlOnTime" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Value of Control ON-Time and Guard Time for external trigger (COTR).</p></html>]]></a:v>
                      </a:a>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS"
                           type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="NXP" />
                      <a:a name="SYMBOLICNAMEVALUE" value="false" />
                      <a:a name="LABEL" value="Ctu Control ON-Time Register Value"/>
                      <a:a name="UUID" value="ECUC:98aa5ae0-cced-45d8-a74e-d5d17268f72d" />
                      <a:da name="DEFAULT" value="0" />
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=255" />
                        <a:tst expr="&gt;=0" />
                      </a:da>
                    </v:var>

                    <!-- Ctu Expected Conversion Duration Configuration Container -->
                    <v:ctr name="ExpectedConvDurationConfig" type="IDENTIFIABLE">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>This container is used for configuring the expected conversion duration for each Adc Unit.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="UUID" value="ECUC:f783f819-7bd7-49e9-81b7-b2ffbc80233a"/>

                      <!-- Ctu Expected Value Conversion Duration for Port A -->
                      <v:var name="CtuExpectedValuePortA" type="INTEGER">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>Expected number of system clock cycles (the slave bus clock) for an AD conversion to be completed by the ADC connected to Port A.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                              type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Duration for Port A"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:52a72a83-8510-4463-8d56-dd54d6a29bcc"/>
                        <a:da name="DEFAULT" value="65535"/>
                        <a:da name="DEFAULT_RADIX" value="HEX"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&gt;=0"/>
                          <a:tst expr="&lt;=65535"/>
                        </a:da>
                      </v:var>

                      <!-- Ctu Out of the Expected Range Notification for Port A -->
                      <v:var name="CtuExpectedNotifPortA" type="FUNCTION-NAME">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This function pointer is called whenever a conversion takes longer than the expected duration to complete by the ADC connected to Port A.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="LABEL" value="Associated Notification for Port A"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:ffb60b3e-d960-4bb7-9716-7fdad565c764"/>
                        <a:da name="DEFAULT" value="NULL_PTR"/>
                      </v:var>

                      <!-- Ctu Expected Value Conversion Duration for Port B -->
                      <v:var name="CtuExpectedValuePortB" type="INTEGER">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>Expected number of system clock cycles (the slave bus clock) for an AD conversion to be completed by the ADC connected to Port B.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                              type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Duration for Port B"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:5c0419d3-6d2a-404c-9f67-10b041f998f4"/>
                        <a:da name="DEFAULT" value="65535"/>
                        <a:da name="DEFAULT_RADIX" value="HEX"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&gt;=0"/>
                          <a:tst expr="&lt;=65535"/>
                        </a:da>
                      </v:var>

                      <!-- Ctu Out of the Expected Range Notification for Port B -->
                      <v:var name="CtuExpectedNotifPortB" type="FUNCTION-NAME">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This function pointer is called whenever a conversion takes longer than the expected duration to complete by the ADC connected to Port B.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="LABEL" value="Associated Notification for Port B"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:e8fb4e1b-bdc1-4e73-86bd-b3bdf5e34728"/>
                        <a:da name="DEFAULT" value="NULL_PTR"/>
                      </v:var>

                      <!-- Ctu CtuConvDurationCounterRange -->
                      <v:var name="CtuConvDurationCounterRange" type="INTEGER">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This value is used to mask out conversion duration values. 1 bits in this value will mask out the corresponding bits in the expected conversion duration registers. These bits must be set from left to right.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                        <a:a name="SCOPE" value="LOCAL"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                              type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                          <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="LABEL" value="Expected conversion duration range"/>
                        <a:a name="ORIGIN" value="NXP"/>
                        <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                        <a:a name="UUID" value="ECUC:013e1d15-8d50-4ac7-a5d0-dc0680dea59c"/>
                        <a:da name="DEFAULT" value="65535"/>
                        <a:da name="DEFAULT_RADIX" value="HEX"/>
                        <a:da name="INVALID" type="Range">
                          <a:tst expr="&gt;=0"/>
                          <a:tst expr="&lt;=65535"/>
                        </a:da>
                      </v:var>
                    </v:ctr> <!-- End of Expected Conversion Duration Configuration Container -->
                    <!-- End of General Tab -->

                    <!-- Ctu Input Trigger Config Tab -->
                    <v:lst name="CtuInputTrigConfigs" type="MAP">
                      <a:da name="MIN" value="1" />
                      <a:da name="MAX" value="8" />
                      <a:a name="LABEL" value="Input Trigger"/>
                      <!-- Ctu Input Trigger Container -->
                      <v:ctr name="CtuInputTrigConfigs" type="IDENTIFIABLE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This container contains the CTU Input Trigger configuration parameters.</p>
                            <p>Select input triggers: even/odd bits correspond to rising/falling edge. Double-buffered (TGSISR)</p></html>]]>
                         </a:v>
                        </a:a>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="UUID" value="ECUC:1e01e2a3-6166-46ba-87c4-0af3e03576ad"/>
                        <!-- Ctu Select Input Triggers -->
                        <v:ref name="CtuInputTrigSelect" type="REFERENCE">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>This parameter selects the input triggers for Trigger Generator Subunit (TGS).</p>
                              <li>Input 0 -    PWM Rel</li>
                              <li>Input 1 -    PWM ODD 0</li>
                              <li>Input 2 -    PWM ODD 1</li>
                              <li>Input 3 -    PWM ODD 2</li>
                              <li>Input 4 -    PWM ODD 3</li>
                              <li>Input 5 -    PWM Even 0</li>
                              <li>Input 6 -    PWM Even 1</li>
                              <li>Input 15 -   External Pin</li>
                              <li>Other Inputs are reserved</li>
                            </html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v class="PostBuild">VariantPostBuild</icc:v>
                            <icc:v class="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="LABEL" value="Ctu Input Signal"/>
                          <a:a name="UUID" value="ECUC:81a2bc7d-1a0c-4ee3-a207-e66a7fdaee18" />
                          <a:da name="REF" value="ASPathDataOfSchema:/TS_T40D11M50I0R0/Adc/AdcConfigSet/AdcHwTrigger"/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="text:uniq(../../*/CtuInputTrigSelect/., .)" false="Duplicated CTU Select Input Trigger."/>
                            <a:tst expr="contains((node:ref(.)/AdcHwTrigSrc), 'EXT_TRIG')" true="The Trigger Source must come from CTU."/>
                          </a:da>
                        </v:ref>
                        <!-- Ctu Select Input Trigger Edge -->
                        <v:var name="CtuInputTrigEdge" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Selects the edge of activation of the input signal.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Input Trigger Edge"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:8e2f2435-2754-4f03-b094-073b363aaa66"/>
                          <a:da name="DEFAULT" value="EDGE_BOTH"/>
                          <a:da name="RANGE">
                            <a:v>EDGE_RISING</a:v>
                            <a:v>EDGE_FALLING</a:v>
                            <a:v>EDGE_BOTH</a:v>
                          </a:da>
                        </v:var>
                      </v:ctr>
                    </v:lst><!-- End of Ctu Input Trigger Config Tab -->

                    <!-- Ctu Trigger Configurations Tab -->
                    <v:lst name="CtuTriggerCfg" type="MAP">
                      <a:da name="MIN" value="1"/>
                      <a:da name="MAX" value="8"/>
                      <a:a name="LABEL" value="Ctu Trigger Configurations"/>
                      <!-- Ctu Trigger Configuration Big Container -->
                      <v:ctr name="CtuTriggerCfg" type="IDENTIFIABLE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This container contains the Ctu configuration that is used for configuring the properties of an internal trigger (TGS output trigger, input for SU).</p></html>]]></a:v>
                        </a:a>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="UUID" value="ECUC:c22eb5d1-3fbb-44e9-bed4-d754b5dda689"/>
                        <!-- Ctu Trigger Index -->
                        <v:var name="CtuTriggerIndex" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>This parameter defines the assignment of the Ctu trigger to the physical CTU hardware triggers</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="LABEL" value="Trigger Index"/>
                          <a:a name="UUID" value="ECUC:64a608cd-a223-48b7-bf0d-9806255aef9e" />
                          <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, '0')" />
                          <a:da name="RANGE" type="XPath">
                             <a:tst expr="(. &gt;= 0) and (. &lt;=7)"
                             false="Value out of range: must be in range 0 to 7. Use the Calc button to calculate correct default value."/>
                             <a:tst expr="text:uniq(../../*/CtuTriggerIndex/., .)" false="Trigger index must be unique in CTU Trigger Configurations array."/>
                          </a:da>
                            <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=7" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu Compare Value -->
                        <v:var name="CtuCompareVal" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Compare Value - double-buffered (TnCR).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Compare Value"/>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="UUID" value="ECUC:5f3a4575-5782-497f-b375-c46d99cdcd34" />
                          <a:da name="DEFAULT" value="0" />
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=65535" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu Adc Command List Start Address -->
                        <v:var name="CtuCmdListStartAdr" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>First address of the command list associated with the internal trigger - (CLCRx). Available when ADCE is enabled.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="First address of the command list"/>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="UUID" value="ECUC:9f59476f-2f32-478f-9c67-2325e47bd0c8" />
                          <a:da name="DEFAULT" value="0" />
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=23" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu Output Trigger Enable Mask -->
                        <v:var name="CtuOutputTrigEnMask" type="INTEGER">
                          <a:a name="DESC">
                              <a:v>
                                  <![CDATA[EN:<html><p>Select the CTU output triggers enabled for each SU input trigger. To be used with CTU_IP_OUTPUT_TRIG_n defines. Double-buffered (THCR).</p>
                                    <ul><p>Trigger Handler Control Register:</p>
                                    <li>Disable -  Bit Mask b0</li>
                                    <li>Tn_E    -  Bit Mask b1000000</li>
                                    <li>Tn_ETE  -  Reserved for this platform</li>
                                    <li>Tn_T4E  -  Reserved for this platform</li>
                                    <li>Tn_T3E  -  Reserved for this platform</li>
                                    <li>Tn_T2E  -  Reserved for this platform</li>
                                    <li>Tn_T1E  -  Reserved for this platform</li>
                                    <li>Tn_ADCE -  Bit Mask b0000001</li>
                                    <li>Tn_ALL  -  Bit Mask b1000001</li></ul></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Output Trigger Enable Mask"/>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="UUID" value="ECUC:150d1863-ad41-40e8-8320-2f6e2b79d41d" />
                          <a:da name="DEFAULT" value="0"/>
                          <a:a name="DEFAULT_RADIX" value="BIN"/>
                          <a:da name="RANGE">
                            <a:v>0</a:v>
                            <a:v>1</a:v>
                            <a:v>64</a:v>
                            <a:v>65</a:v>
                          </a:da>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=65" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu Trigger Notification -->
                        <v:var name="CtuTriggerNotif" type="FUNCTION-NAME">
                          <a:a name="DESC">
                              <a:v><![CDATA[EN:<html><p>Notification called when triggered.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Trigger Notification"/>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="UUID" value="ECUC:b1c8ad15-9a5b-4249-b81d-69b219bb3632" />
                          <a:da name="DEFAULT" value="NULL_PTR"/>
                        </v:var>
                      </v:ctr>
                    </v:lst><!-- End of Ctu Trigger Configurations Tab -->

                    <!-- Ctu Adc Command List Tab -->
                    <v:lst name="CtuAdcCommandList" type="MAP">
                      <a:da name="MIN" value="1" />
                      <a:da name="MAX" value="24" />
                      <a:a name="LABEL" value="Adc Command List"/>
                      <!-- Ctu Adc Command List Big Container -->
                      <v:ctr name="CtuAdcCommandList" type="IDENTIFIABLE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This container contains the Ctu Adc Command List configuration parameters.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="UUID" value="ECUC:3c481c00-656c-4776-bc8b-738dd063671d"/>
                        <!-- Ctu Enable Command Interrupt -->
                        <v:var name="CtuIntEn" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Enable/Disable interrupt request for command execution (CIR).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Interrupt request enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:85390a01-4017-499e-bc4e-c23b02129e45"/>
                          <a:da name="DEFAULT" value="false"/>
                        </v:var>
                        <!-- Ctu FIFO Index -->
                        <v:var name="CtuFifoIdx" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select result FIFO to store the conversion result.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="FIFO index"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:85390a01-4017-499e-bc4e-c23b02129ee5"/>
                          <a:da name="DEFAULT" value="0"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=3" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu Conversion Mode -->
                        <v:var name="CtuConvMode" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select conversion mode.</p>
                            <li>Single conversion mode - conversion will run on a single ADC Unit</li>
                            <li>Dual conversion mode - conversion will run on two ADC units simultaneously</li>
                            </html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Conversion mode"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:22019be2-b5de-40f3-a0ef-2a671a5d5111"/>
                          <a:da name="DEFAULT" value="CONV_MODE_SINGLE"/>
                          <a:da name="RANGE">
                            <a:v>CONV_MODE_SINGLE</a:v>
                            <a:v>CONV_MODE_DUAL</a:v>
                          </a:da>
                        </v:var>
                        <!-- Ctu Last Command -->
                        <v:var name="CtuLastCmd" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Mark this as the last command in the list by setting the last command(LC) bit of the next command to 1.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Last command"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:97173983-2a82-48d5-ac53-7cfe6a8c15eb"/>
                          <a:da name="DEFAULT" value="LAST"/>
                          <a:da name="RANGE">
                            <a:v>LAST</a:v>
                            <a:v>NOT_LAST</a:v>
                          </a:da>
                        </v:var>
                        <!-- Ctu Adc Port -->
                        <v:var name="CtuAdcPort" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select ADC Unit to run the conversion.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="ADC port"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:57f6a4cc-45be-4247-9b0b-0e399f3c1e41"/>
                          <a:da name="DEFAULT" value="ADC_0"/>
                          <a:da name="RANGE">
                            <a:v>ADC_0</a:v>
                            <a:v>ADC_1</a:v>
                          </a:da>
                        </v:var>
                        <!-- CTU Adc Channel for Port A -->
                        <v:var name="CtuAdcChanA" type="ENUMERATION">
                         <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select target channel number for ADC Unit 0 (ADC port A).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="LABEL" value="ADC channel number for PORT A"/>
                          <a:a name="UUID" value="ECUC:545c0d08-3ac3-4e38-994c-93e2ef6a6133"/>
                          <a:da name="DEFAULT" type="XPath" expr="ecu:list('Adc.AdcConfigSet.AdcHwUnit0.AdcChannel.AdcChannelName')[1]" ></a:da>
                          <a:da name="RANGE" type="XPath" expr="ecu:list('Adc.AdcConfigSet.AdcHwUnit0.AdcChannel.AdcChannelName')"/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="((num:i(text:split(., 'ChanNum')[last()])) > 15)"
                              true="Not support channels which have ID greater than 15 because CLR_A_x.CH have 4 bits (x is from 1 to 24)."/>
                          </a:da>
                        </v:var>
                        <!-- CTU Adc Channel for Port B -->
                        <v:var name="CtuAdcChanB" type="ENUMERATION">
                         <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select target channel number for ADC Unit 1 (ADC port B).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="LABEL" value="ADC channel number for PORT B"/>
                          <a:a name="UUID" value="ECUC:0f282d31-d79c-449a-acc2-1f75a772036b"/>
                          <a:da name="DEFAULT" type="XPath" expr="ecu:list('Adc.AdcConfigSet.AdcHwUnit1.AdcChannel.AdcChannelName')[1]" ></a:da>
                          <a:da name="RANGE" type="XPath" expr="ecu:list('Adc.AdcConfigSet.AdcHwUnit1.AdcChannel.AdcChannelName')"/>
                          <a:a name="EDITABLE" type="XPath">
                            <a:tst expr="(ecu:get(&apos;Adc.AdcConfigSet.AdcHwUnit&apos;) &gt; 1) and (../CtuConvMode = 'CONV_MODE_DUAL')"/>
                          </a:a>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="((substring-before(., 'ChanNum') = substring-before(../CtuAdcChanA, 'ChanNum')) and (../CtuConvMode = 'CONV_MODE_DUAL'))"
                              true="The same physical channel cannot be assigned for both ADCs in a dual conversion mode."/>
                            <a:tst expr="((num:i(text:split(., 'ChanNum')[last()])) > 15)"
                              true="Not support channels which have ID greater than 15 because CLR_B_x.CH_A and CLR_B_x.CH_B have 4 bits."/>
                          </a:da>
                        </v:var>
                      </v:ctr>
                    </v:lst> <!-- End of Ctu Adc Command List Tab -->

                    <!-- Ctu Fifos Config Tab -->
                    <v:lst name="CtuResultFifos" type="MAP">
                      <a:da name="MIN" value="1" />
                      <a:da name="MAX" value="4" /><!-- TODO: use value from resources type="XPath" expr="ecu:get('rs')"/> -->
                      <a:a name="LABEL" value="Ctu Result FIFOs"/>
                      <!-- Ctu Fifo Container -->
                      <v:ctr name="CtuResultFifos" type="IDENTIFIABLE">
                        <a:a name="DESC">
                          <a:v><![CDATA[EN:<html><p>This container contains the Ctu Fifos configuration parameters.</p></html>]]></a:v>
                        </a:a>
                        <a:a name="REQUIRES-INDEX" value="true"/>
                        <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                        <a:a name="IMPLEMENTATIONCONFIGCLASS"
                             type="IMPLEMENTATIONCONFIGCLASS">
                          <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                          <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        </a:a>
                        <a:a name="UUID" value="ECUC:33e08c71-80c2-438b-9e4b-2232a91f050d"/>
                        <!-- Ctu Fifo Index -->
                        <v:var name="CtuFifoIndex" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>The FIFO index of CTU.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="LABEL" value="Fifo Index"/>
                          <a:a name="UUID" value="ECUC:0e4862ae-2e0c-485f-b432-8f3d5e9f4969" />
                          <a:da name="DEFAULT" type="XPath" expr="node:fallback(node:current()/../@index, '0')" />
                          <a:da name="RANGE" type="XPath">
                             <a:tst expr="text:uniq(../../*/CtuFifoIndex, .)"
                                  false="Duplicated value, the Fifo Index must be unique across all Ctu Fifos. Use the Calc button to calculate correct default value."/>
                          </a:da>
                            <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=3" />
                            <a:tst expr="&gt;=0" />
                          </a:da>
                          <a:da name="INVALID" type="XPath">
                            <!-- TODO: Need to make sure that the the Ctu Fifo was configured in Mcl module? -->
                          </a:da>
                        </v:var>
                        <!-- PR-MCAL-3296.adc -->
                        <!-- Ctu Fifo Threshold -->
                        <v:var name="CtuFifoThreshold" type="INTEGER">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Threshold value of the fifo. FIFOs generate an interrupt when the number of elements in the FIFO exceeds the values present in the threshold register.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="LABEL" value="Fifo Threshold"/>
                          <a:a name="UUID" value="ECUC:f06611ae-748b-40a0-9409-e580303fad91" />
                          <a:da name="DEFAULT" value="8"/>
                          <a:da name="INVALID" type="Range">
                            <a:tst expr="&lt;=ecu:get('AdcMaxCTUFifoDepth')"/>
                            <a:tst expr="&gt;=0" />
                          </a:da>
                        </v:var>
                        <!-- Ctu DMA Enable -->
                        <v:var name="CtuFifoDmaEn" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Enable DMA requests to be issued when the fifoThreshold is reached - (FDCR.DEn).</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP" />
                          <a:a name="SYMBOLICNAMEVALUE" value="false" />
                          <a:a name="LABEL" value="DMA Enable"/>
                          <a:a name="UUID" value="ECUC:b16708e5-f559-478b-a032-1528e02d00a7" />
                          <a:da name="DEFAULT" value="false" />
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="(. = 'true') and (../../../../../../AutosarExt/CtuEnableDmaTransferMode ='false')"
                                  true="Adc CTU Control Mode Enable DMA support (Adc/AutosarExt/CtuEnableDmaTransferMode) must be enabled first in order to use this feature"/>
                          </a:da>
                        </v:var>

                        <!-- Ctu Fifo Dma User Buffer -->
                        <!--  @implements CtuFifoDmaBuffer_Object  -->
                        <v:var name="CtuFifoDmaBuffer" type="LINKER-SYMBOL">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Pointer to the Data Buffer (destination for conversion results) when CtuFifoDmaEn is enabled.</p></html>]]></a:v>
                         </a:a>
                         <a:a name="SCOPE" value="LOCAL"/>
                         <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="DMA Fifo Buffer Pointer"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:8f60ff19-9ef9-4c89-881f-53e2162bd9b1"/>
                          <a:da name="DEFAULT" type="XPath" expr="concat('CtuDmaFifo', string(node:fallback(node:current()/../CtuFifoIndex,'0')))"/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr=".=''" true = "Invalid value: empty buffer name."/>
                            <a:tst expr="text:uniq(../../../CtuResultFifos/*[CtuFifoDmaEn = 'true']/CtuFifoDmaBuffer, .)" false= "Dma User Fifo Buffer must be unique within its parent list!"/>
                          </a:da>
                          <a:a name="EDITABLE" type="XPath" expr="../CtuFifoDmaEn/.='true'"/>
                        </v:var>
                        <!-- Ctu Fifo Dma Channel Id -->
                        <v:ref name="CtuFifoDmaChannelId" type="REFERENCE">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Select Logical Dma Channel that will be used to transfer the Fifo Buffer from FIFO Result Data Register to the User Fifo Buffer.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                              type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v mclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="OPTIONAL" value="true"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Select Dma Channel for Fifo"/>
                          <a:a name="UUID" value="ECUC:3895bfec-83e0-4c0b-99ea-fd04989f4262"/>
                          <a:da name="REF" value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/Mcl/MclConfig/dmaLogicChannel_Type"/>
                          <a:a name="EDITABLE" type="XPath" expr="../CtuFifoDmaEn/."/>
                          <a:da name="INVALID" type="XPath">
                            <a:tst expr="not(node:refvalid(.)) and ../CtuFifoDmaEn/.='true'" true="Invalid XPath or empty reference. Need to select Dma Channel when CtuFifoDmaEn is enabled"/>
                          </a:da>
                        </v:ref>
                        <!-- Ctu Fifo Threshold Notification -->
                        <v:var name="CtuFifoThresholdNotif" type="FUNCTION-NAME">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>This function pointer is called when the number of elements in Fifo is greater than threshold value.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Fifo threshold notification"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:6d9eef89-9109-4155-a768-6ebe1e870160"/>
                          <a:da name="DEFAULT" value="NULL_PTR"/>
                        </v:var>
                        <!-- Ctu Fifo Underrun Notification -->
                        <v:var name="CtuFifoUnderrunNotif" type="FUNCTION-NAME">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>This function is called when the Fifo becomes empty.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="LABEL" value="Fifo empty notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:ae291e6d-08e9-49c0-84fb-d578c4e6af5e"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath" expr="(../CtuFifoDmaEn/.='false')"/>
                        </v:var>
                        <!-- Ctu Fifo Overrun Notification -->
                        <v:var name="CtuFifoOverrunNotif" type="FUNCTION-NAME">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>This function is called when the Fifo is full and a write is attempted.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="LABEL" value="Fifo overrun notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:43ead815-cc90-489a-b331-a99a6b8be9ef"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath" expr="(../CtuFifoDmaEn/.='false')"/>
                        </v:var>
                        <!-- Ctu Fifo Full Notification -->
                        <v:var name="CtuFifoFullNotif" type="FUNCTION-NAME">
                            <a:a name="DESC">
                                <a:v><![CDATA[EN:<html><p>This function is called when the Fifo becomes full.</p></html>]]></a:v>
                            </a:a>
                            <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                            <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                                <icc:v vclass="PostBuild">VariantPostBuild</icc:v>
                                <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                            </a:a>
                            <a:a name="SCOPE" value="LOCAL"/>
                            <a:a name="LABEL" value="Fifo full notification"/>
                            <a:a name="ORIGIN" value="NXP"/>
                            <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                            <a:a name="UUID" value="ECUC:826a69d9-4ba8-409c-a417-42efbacda75d"/>
                            <a:da name="DEFAULT" value="NULL_PTR"/>
                            <a:a name="EDITABLE" type="XPath" expr="(../CtuFifoDmaEn/.='false')"/>
                        </v:var>
                      </v:ctr> <!-- End of Ctu Fifo Container -->
                    </v:lst> <!-- End of Ctu Fifos Config Tab -->

                  </v:ctr> <!-- End of CtuHwUnit Big container -->
                </v:lst> <!-- End of CtuHwUnit List -->
              </v:ctr>


              <!-- AdcGeneral Big Container -->
              <!-- ECUC_Adc_00027 -->
              <!-- /** @implements AdcGeneral_Object */ -->
              <v:ctr name="AdcGeneral" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>General configuration (parameters) of the ADC Driver software module.</p></html>]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:562d0b86-f75f-4191-8dd5-36144c6aa5b3"/>

                <!-- AdcDeInitApi -->
                <!-- ECUC_Adc_00404, SWS_Adc_00228 -->
                <v:var name="AdcDeInitApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds/removes the service Adc_DeInit() from the code.
                            <li>true: Adc_DeInit() can be used.</li>
                            <li>false: Adc_DeInit() can not be used.</li></p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc_DeInit API"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:53b4c81e-4a82-4a57-b508-cef1d8b8a271"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>

                <!-- AdcDevErrorDetect -->
                <!-- ECUC_Adc_00405 -->
                <v:var name="AdcDevErrorDetect" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Switches the development error detection and notification on or off.
                            <li>true: detection and notification is enabled.</li>
                            <li>false: detection and notification is disabled.</li></p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Development Error Detection"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:ad7c7dd4-d249-450b-945c-e486438ee70a"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- AdcEnableLimitCheck -->
                <!-- ECUC_Adc_00452 -->
                <v:var name="AdcEnableLimitCheck" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Enables or disables limit checking feature in the ADC driver.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Limit Check"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:aa97c98c-3c97-4e18-9343-058d81b68fba"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>

                <!-- AdcEnableQueuing -->
                <!-- ECUC_Adc_00391, SWS_Adc_00333 -->
                <v:var name="AdcEnableQueuing" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Determines, if the queuing mechanism is active in case of priority mechanism disabled.
                        Note: If priority mechanism is enabled, queuing mechanism is always active and the parameter ADC_ENABLE_QUEUING is not evaluated.
                        <li>true: Enabled.</li>
                        <li>false: Disabled.</li></p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Queue"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:1d991cd4-edbb-46f9-b66a-2685b436c91a"/>
                  <a:da name="DEFAULT" value="true"/>
                  <a:a name="EDITABLE" type="XPath">
                    <a:tst expr="../AdcPriorityImplementation = 'ADC_PRIORITY_NONE'"/>
                  </a:a>
                </v:var>

                <!-- AdcPriorityQueueMaxDepth -->
                <v:var name="AdcPriorityQueueMaxDepth" type="INTEGER">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Maximum depth of queue used for queuing of incoming conversion requests when hardware unit is busy.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Max Queue Depth"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:33cfe352-1333-43a6-b6d2-54239b433f1b"/>
                  <a:da name="DEFAULT" value="1"/>
                  <a:da name="RANGE" type="XPath">
                    <a:tst expr="../AdcPriorityImplementation = 'ADC_PRIORITY_NONE' and ../AdcEnableQueuing = 'false' and . !=1" true="The depth must be 1 if the AdcGeneral/AdcEnableQueuing is disabled and AdcGeneral/AdcPriorityImplementation is set as ADC_PRIORITY_NONE."/>
                  </a:da>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=1024"/>
                    <a:tst expr="&gt;=1"/>
                  </a:da>
                </v:var>

                <!-- AdcEnableStartStopGroupApi -->
                <!-- ECUC_Adc_00406, SWS_Adc_00259, SWS_Adc_00260, SWS_Adc_00356 -->
                <v:var name="AdcEnableStartStopGroupApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds / removes the services Adc_StartGroupConversion() and Adc_StopGroupConversion() from the code.
                      <li>true: Adc_StartGroupConversion() and Adc_StopGroupConversion() can be used.</li>
                      <li>false: Adc_StartGroupConversion() and Adc_StopGroupConversion() can not be used.</li>
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc_StartStopGroup API"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c0f7c400-2e84-4b40-a9f3-7a045ac15e7f"/>
                  <a:da name="DEFAULT" value="true"/>
                  <a:a name="INVALID" type="XPath">
                    <a:tst expr="(. = 'false') and (../AdcHwTriggerApi = 'false')"
                           true="At least one of 2 parameters AdcEnableStartStopGroupApi or AdcHwTriggerApi has to be enabled in order to be able to use the ADC driver."/>
                  </a:a>
                </v:var>

                <!-- AdcGrpNotifCapability -->
                <!-- ECUC_Adc_00105, SWS_Adc_00100, SWS_Adc_00101 -->
                <v:var name="AdcGrpNotifCapability" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Determines, if the group notification mechanism (the functions to enable and disable the notifications) is available at runtime.
                        <li>true: Enabled.</li>
                        <li>false: Disabled.</li></p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Notification Capability"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:2a246d2c-09d2-450b-b58e-5f962fdebff1"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>

                <!-- AdcHwTriggerApi -->
                <!-- ECUC_Adc_00408, SWS_Adc_00265, SWS_Adc_00266 -->
                <v:var name="AdcHwTriggerApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds / removes the services Adc_EnableHardwareTrigger() and Adc_DisableHardwareTrigger() from the code.
                      <li>true: Adc_EnableHardwareTrigger() and Adc_DisableHardwareTrigger() can be used.</li>
                      <li>false: Adc_EnableHardwareTrigger() and Adc_DisableHardwareTrigger() can not be used.</li>
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Hw Trigger API"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:31df0f6a-38f4-4901-8e02-8a3464617315"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- AdcPriorityImplementation -->
                <!-- ECUC_Adc_00393, SWS_Adc_00315, SWS_Adc_00340 -->
                <v:var name="AdcPriorityImplementation" type="ENUMERATION">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Determines whether a priority mechanism is available for prioritization of the conversion requests and if available, the type of prioritization mechanism. The selection applies for groups with trigger source software and trigger source hardware. Two types of prioritization mechanism can be selected.
                      The hardware prioritization mechanism (AdcPriorityHw) uses the ADC hardware features for prioritization of the software conversion requests and hardware trigger signals for groups with trigger source hardware.
                      The mixed hardware and software prioritization mechanism (AdcPriorityHwSw) uses the ADC hardware features for prioritization of ADC hardware trigger for groups with trigger source hardware and a software implemented prioritization mechanism for groups with trigger source software.
                      The group priorities for software triggered groups are typically configured with lower priority levels than the group priorities for hardware triggered groups.
                      ImplementationType: Adc_PriorityImplementationType.
                      Note: In this version the ADC_PRIORITY_HW isn't used.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Priority Mechanism"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:5c9c40fe-a13c-4501-8290-443b3417ebdd"/>
                  <a:da name="DEFAULT" value="ADC_PRIORITY_NONE"/>
                  <a:da name="RANGE">
                    <a:v>ADC_PRIORITY_HW</a:v>
                    <a:v>ADC_PRIORITY_HW_SW</a:v>
                    <a:v>ADC_PRIORITY_NONE</a:v>
                  </a:da>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr=".='ADC_PRIORITY_HW'"
                    true="Priority Hardware mechanism is not supported."/>
                  </a:da>
                </v:var>

                <!-- AdcReadGroupApi -->
                <!-- ECUC_Adc_00394, SWS_Adc_00359, SWS_Adc_00383 -->
                <v:var name="AdcReadGroupApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds / removes the service Adc_ReadGroup() from the code.
                      <li>true: Adc_ReadGroup() can be used.</li>
                      <li>false: Adc_ReadGroup() can not be used.</li>
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc_ReadGroup API"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d77cec18-b843-4253-a377-0609bbb8935e"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>

                <!-- AdcResultAlignment -->
                <!-- ECUC_Adc_00444, CPR-MCAL-740.adc -->
                <v:var name="AdcResultAlignment" type="ENUMERATION">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Alignment of ADC raw results in ADC result buffer (left/right alignment).
                      Implementation Type: Adc_ResultAlignmentType.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Result Alignment"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:aa2aec7d-5ef6-4d53-838a-1712f99bf11c"/>
                  <a:da name="DEFAULT" value="ADC_ALIGN_RIGHT"/>
                  <a:da name="RANGE">
                    <a:v>ADC_ALIGN_RIGHT</a:v>
                    <a:v>ADC_ALIGN_LEFT</a:v>
                  </a:da>
                </v:var>

                <!-- AdcVersionInfoApi -->
                <!-- ECUC_Adc_00409 -->
                <v:var name="AdcVersionInfoApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds / removes the service Adc_GetVersionInfo() from the code.
                      <li>true: Adc_GetVersionInfo() can be used.</li>
                      <li>false: Adc_GetVersionInfo() can not be used.</li>
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc_VersionInfo API"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58da3"/>
                  <a:da name="DEFAULT" value="true"/>
                </v:var>

                <!-- ECUC_Adc_00463 -->
                <!--  @implements AdcEcucPartitionRef_Object  -->
                <v:lst name="AdcEcucPartitionRef">
                  <a:a name="INVALID" type="XPath">
                     <a:tst expr="((../../AutosarExt/AdcMulticoreSupport = 'false') and (num:i(count(./*)) > 0)) or ((../../AutosarExt/AdcMulticoreSupport = 'true') and (num:i(count(./*)) = 0))"
                           true="When at least one EcucPartitions is defined, a cross check should be done with AdcMulticoreSupport to be enabled."/>
                     <a:tst expr="((../../AutosarExt/AdcMulticoreSupport = 'true') and (num:i(count(./*)) > ../../AutosarExt/AdcMaxPartitions))"
                           true="Number of partitions configured exceeds value configured in Maximum partitions number from AutosarExt/AdcMaxPartitions."/>
                  </a:a>
                  <a:a name="EDITABLE" type="XPath">
                        <a:tst expr="../../AutosarExt/AdcMulticoreSupport ='true'"/>
                  </a:a>
                  <v:ref name="AdcEcucPartitionRef" type="REFERENCE">
                    <a:a name="DESC">
                      <a:v>Maps the ADC driver to zero or multiple ECUC partitions to make the driver API available in the according partition.</a:v>
                      <a:v>Tags: atp.Status=draft</a:v>
                    </a:a>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS"
                         type="IMPLEMENTATIONCONFIGCLASS">
                      <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                      <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                      <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                      <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                    <a:a name="REQUIRES-INDEX" value="true"/>
                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                    <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                    <a:a name="SCOPE" value="ECU"/>
                    <a:a name="UUID"
                         value="ECUC:f86d720b-a1e5-4f3f-a656-0f49601b8563"/>
                    <a:da name="REF"
                          value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                    <a:da name="INVALID" type="XPath">
                          <a:tst expr="count(text:grep(../../../AdcConfigSet/AdcHwUnit/*/AdcHwUnitEcucPartitionRef/*[1], .)) = 0"
                               true="The Ecuc partition need to be mapped to at least one ADC hardware unit."/>
                          <a:tst expr="node:containsValue(as:modconf('Os')[1]/OsApplication/*/OsAppEcucPartitionRef, .)"
                               false="The referenced ECUC partition isn't used by any OsApplication (i.e. Os/OsApplication/*/OsAppEcucPartitionRef)"/>
                          <a:tst expr="text:uniq(../*, .)" false="Duplicate Partition."/>
                    </a:da>
                  </v:ref>
                </v:lst>

                <!--  This node is required by Autosar but set editable false because not supported by driver implementation  -->
                <!-- ECUC_Adc_00464 -->
                <!--  @implements AdcKernelEcucPartitionRef_Object  -->
                <v:ref name="AdcKernelEcucPartitionRef" type="REFERENCE">
                  <a:a name="EDITABLE" value="false"/>
                  <a:a name="DESC">
                    <a:v>Maps the ADC kernel to zero or one ECUC partition to assign the driver kernel to a certain core. The ECUC partition referenced is a subset of the ECUC partitions where the ADC driver is mapped to.</a:v>
                    <a:v>Tags: atp.Status=draft</a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="true"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="true"/>
                  <a:a name="SCOPE" value="ECU"/>
                  <a:a name="UUID"
                       value="ECUC:2257ea54-1810-4b76-84b2-e12939218b57"/>
                  <a:da name="REF"
                        value="ASPathDataOfSchema:/AUTOSAR/EcucDefs/EcuC/EcucPartitionCollection/EcucPartition"/>
                  <a:a name="OPTIONAL" value="true"/>
                </v:ref>

                <!--  ECUC_Adc_00457 -->
                <!-- @implements AdcLowPowerStatesSupport_Object -->
                <v:var name="AdcLowPowerStatesSupport" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds / removes all power state management related APIs (ADC_SetPowerState, ADC_GetCurrentPowerState, ADC_GetTargetPowerState, ADC_PreparePowerState, ADC_Main_PowerTransitionManager), indicating if the HW offers low power state management.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Low Power States Support"/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:e2930cb1-2824-42e0-84fd-4e72fb180e7d"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- ECUC_Adc_00458 -->
                <!-- @implements AdcPowerStateAsynchTransitionMode_Object -->
                <v:var name="AdcPowerStateAsynchTransitionMode" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Enables / disables support of the ADC Driver to the asynchronous power state transition. This feature is not implemented on this platform.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v class="PreCompile">VariantPostBuild</icc:v>
                    <icc:v class="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Power State Asynch Transition Mode"/>
                  <a:a name="OPTIONAL" value="true"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:c20b0644-70bf-435d-ace6-8626ec469675"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:da name="EDITABLE" type="XPath" expr="(node:exists(../AdcLowPowerStatesSupport) and (../AdcLowPowerStatesSupport = 'true'))"/>
                  <a:da name="READONLY" value="true"/>
                </v:var>

                <!-- @implements ECUC_Adc_00459 -->
                <!-- @implements AdcPowerStateConfig_Object -->
                <v:lst name="AdcPowerStateConfig" type="MAP">
                  <a:da name="EDITABLE"  type="XPath" expr="(node:exists(../AdcPowerStateAsynchTransitionMode) and (../AdcPowerStateAsynchTransitionMode = 'true' ))"/>
                  <v:ctr name="AdcPowerStateConfig" type="IDENTIFIABLE">
                    <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Each instance of this parameter defines a power state and the callback to be called when this power state is reached.</p></html>]]></a:v>
                    </a:a>
                    <a:a name="POSTBUILDVARIANTMULTIPLICITY" value="false"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                    </a:a>
                    <a:a name="UUID" value="ECUC:66904f78-ff07-4ed3-878e-401a3430cd38"/>

                    <!-- @implements ECUC_Adc_00461 -->
                    <!-- @implements AdcPowerState_Object -->
                    <v:var name="AdcPowerState" type="INTEGER">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Each instance of this parameter describes a different power state
                             supported by the ADC HW. It should be defined by the HW supplier and
                             used by the ADCDriver to reference specific HW configurations which set
                             the ADC HW module in the referenced power state. At least the power mode
                             corresponding to full power state shall be always configured.
                             This parameter shall only be configured if the parameter AdcLowPowerStatesSupport is set to true.</p></html>]]>
                        </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="true"/>
                      <a:a name="UUID"  value="ECUC:41902199-e783-4afe-bef3-41c69e08b32d"/>
                      <a:da name="DEFAULT" value="0"/>
                      <a:da name="INVALID" type="Range">
                        <a:tst expr="&lt;=9223372036854775807"/>
                        <a:tst expr="&gt;=0"/>
                      </a:da>
                    </v:var>

                    <!-- @implements ECUC_Adc_00460 -->
                    <!-- @implements AdcPowerStateReadyCbkRef_Object -->
                    <v:var name="AdcPowerStateReadyCbkRef" type="FUNCTION-NAME">
                      <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>Each instance of this parameter contains a reference to a power mode callback defined in a CDD or IoHwAbs component.
                             This parameter shall only be configured if the parameter AdcLowPowerStatesSupport is set to true</p></html>]]>
                        </a:v>
                      </a:a>
                      <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                        <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                      </a:a>
                      <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                      <a:a name="SCOPE" value="LOCAL"/>
                      <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                      <a:a name="UUID" value="ECUC:b9c222fc-6b92-40e9-922e-f73addaf2320"/>
                      <a:da name="DEFAULT" value="NULL_PTR"/>
                    </v:var>
                  </v:ctr>
                </v:lst>
              </v:ctr>

              <v:lst name="AdcHwConfiguration" type="MAP">
                    <a:da name="MIN" value="1"/>
                    <v:ctr name="AdcHwConfiguration" type="IDENTIFIABLE">
                      <a:a name ="DESC">
                      <a:v>
                          <![CDATA[<html><p>Selects whether the interrupt for each ADC Unit will be enabled. These settings are used for optimizing the code size by removing the interrupt handling code for interrupts that are not needed.</p></html>]]></a:v>
                      </a:a>
                      <a:a name="UUID" value="ECUC:0cb2283c-677d-434e-80a6-169b5c6e63e9"/>
                      <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                        <icc:v mclass="PreCompile">VariantPreCompile</icc:v>
                        <icc:v mclass="PreCompile">VariantPostBuild</icc:v>
                      </a:a>

                        <!-- AdcHwConfiguredId -->
                        <v:var name="AdcHwConfiguredId" type="ENUMERATION">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Numeric ID of the HW Unit. This symbolic name allows accessing Hw Unit data. Enumeration literals are defined vendor specific.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                              type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="LABEL" value="Adc Hardware Unit"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:f8f69c4d-d49e-4c7f-8651-5f7cfc5e35b8"/>
                        <a:da name="DEFAULT" type="XPath" expr="concat('ADC', string(node:fallback(node:current()/../@index,'0')))"/>
                        <a:da name="RANGE">
                            <a:v>ADC0</a:v>
                            <a:v>ADC1</a:v>
                        </a:da>
                          <a:da name="INVALID" type="XPath">
                              <a:tst expr="text:uniq(../*/AdcHwUnitId, .)" false="Duplicate Hw Unit id"/>
                          </a:da>
                        </v:var>


                        <!-- Adc Normal Interrupt Enable -->
                        <v:var name="AdcNormalInterruptEnable" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Adds / removes the Normal interrupt handling routine from the ADC driver code.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Adc Normal Interrupt Enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58dbb"/>
                          <a:da name="DEFAULT" value="false"/>
              
                        </v:var>

                        <!-- Adc Injected Interrupt Enable -->
                        <v:var name="AdcInjectedInterruptEnable" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Adds / removes the Injected interrupt handling routine from the ADC driver code.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="Adc Injected Interrupt Enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58dbc"/>
                          <a:da name="DEFAULT" value="false"/>
                        </v:var>

                        <!-- Adc CTU FIFO OF ISR Interrupt Enable -->
                        <v:var name="CtuFifoOfInterruptEnable" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Adds / removes the CTU FIFO OF ISR interrupt handling routine from the ADC driver code.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="CTU FIFO OF ISR Interrupt Enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58dbd"/>
                          <a:da name="DEFAULT" value="false"/>
                        </v:var>

                        <!-- Adc WDG Threhold Enable -->
                        <v:var name="WdgThresholdEnable" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Adds / removes the WDG Threshold usage from the ADC driver code.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="WDG Threshold Enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58dbe"/>
                          <a:da name="DEFAULT" value="false"/>
                        </v:var>

                        <!-- Adc DMA transfer Enable -->
                        <v:var name="DmaTransferEnable" type="BOOLEAN">
                          <a:a name="DESC">
                            <a:v><![CDATA[EN:<html><p>Adds / removes the DMA transfer usage from the ADC driver code.</p></html>]]></a:v>
                          </a:a>
                          <a:a name="SCOPE" value="LOCAL"/>
                          <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                          <a:a name="IMPLEMENTATIONCONFIGCLASS"
                               type="IMPLEMENTATIONCONFIGCLASS">
                            <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                            <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                          </a:a>
                          <a:a name="LABEL" value="DMA transfer Enable"/>
                          <a:a name="ORIGIN" value="NXP"/>
                          <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                          <a:a name="UUID" value="ECUC:15707a07-29a2-4f44-9d3a-823d0cd58dbf"/>
                          <a:da name="DEFAULT" value="false"/>
                        </v:var>
                   </v:ctr>
                </v:lst>
              <!-- ECUC_Adc_00030 -->
              <!-- /** @implements AdcPublishedInformation_Object */ -->
              <v:ctr name="AdcPublishedInformation" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>Additional published parameters not covered by CommonPublishedInformation container.
                    Note that these parameters do not have any configuration class setting, since they are published information.</p></html>]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:b9918606-a5e5-4d61-8487-2f24936f3d8e"/>

                <!-- AdcChannelValueSigned */ -->
                <!-- ECUC_Adc_00410 -->
                <v:var name="AdcChannelValueSigned" type="BOOLEAN_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Information whether the result value of the ADC driver has sign information (true) or not (false). If the result shall be interpreted as signed value it shall apply to C-language rules.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Channel Value Signed"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d34c315d-5b68-49ef-956e-f93b26c72fce"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!--  AdcGroupFirstChannelFixed */ -->
                <!--  ECUC_Adc_00411 -->
                <v:var name="AdcGroupFirstChannelFixed" type="BOOLEAN_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Information whether the first channel of an ADC Channel group can be configured (false) or is fixed (true) to a value determined by the ADC HW Unit.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Group First Channel Fixed"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d16ed07a-7b8d-4c64-bca3-c5c4250474ab"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!--  AdcMaxChannelResolution */ -->
                <!--  ECUC_Adc_00412 -->
                <v:var name="AdcMaxChannelResolution" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Maximum Channel resolution in bits (does not specify accuracy).</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Max Channel Resolution"/>
                  <a:a name="ORIGIN" value="AUTOSAR_ECUC"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:78834145-77e3-41f8-a1da-ea6ad4ee1883"/>
                  <a:da name="DEFAULT" value="12"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=63"/>
                    <a:tst expr="&gt;=1"/>
                  </a:da>
                </v:var>
              </v:ctr>

              <!-- PR-MCAL-3120.adc, PR-MCAL-3095.adc -->
              <!--  @implements CommonPublishedInformation_Object  -->
              <v:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>
                      Common container, aggregated by all modules. It contains published information about vendor and versions.
                  </p></html>]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:56f9ec5b-445f-41d5-b907-d38366749eab"/>
                <!--   @implements ArReleaseMajorVersion_Object   -->
                <v:var name="ArReleaseMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      Major version number of AUTOSAR specification on which the appropriate implementation is based on.
                      </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:fda4ea31-11db-4bcc-bcbd-8c8507f8b0f5"/>
                  <a:da name="DEFAULT" value="4"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=4"/>
                    <a:tst expr="&lt;=4"/>
                  </a:da>
                </v:var>
                <!--  @implements ArReleaseMinorVersion_Object   -->
                <v:var name="ArReleaseMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      Minor version number of AUTOSAR specification on which the appropriate implementation is based on.
                      </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:0626f834-0a47-4b1b-b6f3-74c527d9f3ca"/>
                  <a:da name="DEFAULT" value="4"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=4"/>
                      <a:tst expr="&lt;=4"/>
                  </a:da>
                </v:var>
                <!--  @implements ArReleaseRevisionVersion_Object   -->
                <v:var name="ArReleaseRevisionVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      Revision version number of AUTOSAR specification on which the appropriate implementation is based on.
                      </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:fd4a5223-f6f7-4de7-b91f-f772d10c919f"/>
                  <a:da name="DEFAULT" value="0"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=0"/>
                      <a:tst expr="&lt;=0"/>
                  </a:da>
                </v:var>
                <!--   @implements ModuleId_Object   -->
                <v:var name="ModuleId" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      Module ID of this module from Module List.
                      </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:dd03f1ea-8869-45ad-8950-f520fbb6d8a9"/>
                  <a:da name="DEFAULT" value="123"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=123"/>
                      <a:tst expr="&lt;=123"/>
                  </a:da>
                </v:var>
                <!--   @implements SwMajorVersion_Object   -->
                <v:var name="SwMajorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      Major version number of the vendor specific implementation of the module. The numbering is vendor specific.
                      </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:d573ae14-6adf-4a88-bcf5-b3734c2c309e"/>
                  <a:da name="DEFAULT" value="5"/>
                  <a:da name="INVALID" type="Range">
                      <a:tst expr="&gt;=5"/>
                      <a:tst expr="&lt;=5"/>
                  </a:da>
                </v:var>
                <!--   @implements SwMinorVersion_Object  -->
                <v:var name="SwMinorVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                    Minor version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:72273f10-5242-493f-8a12-e8237da60671"/>
                  <a:da name="DEFAULT" value="0"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>
                </v:var>
                <!--   @implements SwPatchVersion_Object  -->
                <v:var name="SwPatchVersion" type="INTEGER_LABEL">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                    Patch level version number of the vendor specific implementation of the module. The numbering is vendor specific.
                    </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:dc7bd1d8-12a9-4f13-aa3e-0159e0e4ac88"/>
                  <a:da name="DEFAULT" value="0"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&gt;=0"/>
                    <a:tst expr="&lt;=0"/>
                  </a:da>
                </v:var>
                <!--   @implements VendorApiInfix_Object   -->
                <v:var name="VendorApiInfix" type="STRING_LABEL">
                  <a:a name="DESC">
                      <a:v><![CDATA[EN:<html><p>
                      In driver modules which can be instantiated several times on a single ECU, BSW00347 requires that the name of APIs is extended by the VendorId and a vendor specific name.
                      This parameter is used to specify the vendor specific name. In total, the implementation specific name is generated as follows:
                      &lt;ModuleName&gt;_&gt;VendorId&gt;_&lt;VendorApiInfix&gt;&lt;Api name from SWS&gt;.
                      E.g.  assuming that the VendorId of the implementor is 123 and the implementer chose a VendorApiInfix of &quot;v11r456&quot; a api name Can_Write defined in the SWS will translate to Can_123_v11r456Write.
                      This parameter is mandatory for all modules with upper multiplicity &gt; 1. It shall not be used for modules with upper multiplicity =1.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:b9a82eb5-9994-4054-a60e-fdc9ca66badc"/>
                  <a:da name="DEFAULT" value=""/>
                  <a:da name="READONLY" value="true"/>
                </v:var>
                <!--   @implements VendorId_Object  -->
                <v:var name="VendorId" type="INTEGER_LABEL">
                    <a:a name="DESC">
                        <a:v><![CDATA[EN:<html><p>
                          Vendor ID of the dedicated implementation of this module according to the AUTOSAR vendor list.
                        </p></html>]]></a:v>
                    </a:a>
                    <a:a name="SCOPE" value="LOCAL"/>
                    <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                    <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PublishedInformation">VariantPostBuild</icc:v>
                    <icc:v vclass="PublishedInformation">VariantPreCompile</icc:v>
                  </a:a>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:59ee01f9-9acf-46f8-8f19-c044f33cca4f"/>
                    <a:da name="DEFAULT" value="43"/>
                    <a:da name="INVALID" type="Range">
                        <a:tst expr="&gt;=43"/>
                        <a:tst expr="&lt;=43"/>
                    </a:da>
                </v:var>
              </v:ctr>

              <!-- AutosarExt Big Container -->
              <v:ctr name="AutosarExt" type="IDENTIFIABLE">
                <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>Autosar Extension API settings.</p></html>]]></a:v>
                </a:a>
                <a:a name="UUID" value="ECUC:a840d02b-4967-4e98-8e41-b89fd1272780"/>

                <!-- @implements Adc Timeout Method -->
                <v:var name="AdcTimeoutMethod" type="ENUMERATION">
                  <a:a name="LABEL" value="Adc Timeout Method"/>
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html>
                      <p>Configures the timeout method for Adc.</p>
                      <p>Based on this selection a certain timeout method from OsIf will be used in the driver.</p>
                      <p>Note: If OSIF_COUNTER_SYSTEM or OSIF_COUNTER_CUSTOM are selected make sure the corresponding timer is enabled in OsIf General configuration. </p>
                      <p>Note: Implementation Specific Parameter.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                  <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:040e7ff3-d837-4037-9a81-c284bbad9a32"/>
                  <a:a name="DEFAULT" value="OSIF_COUNTER_DUMMY"/>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="node:refs('ASPathDataOfSchema:/TS_T40D11M50I0R0/BaseNXP/OsIfGeneral/OsIfUseCustomTimer') = 'false' and node:fallback(.,'OSIF_COUNTER_DUMMY') = 'OSIF_COUNTER_CUSTOM'" true="Custom Timer is not enabled in OsIf (OsIfGeneral/OsIfUseCustomTimer checkbox)"/>
                    <a:tst expr="node:refs('ASPathDataOfSchema:/TS_T40D11M50I0R0/BaseNXP/OsIfGeneral/OsIfUseSystemTimer') = 'false' and node:fallback(.,'OSIF_COUNTER_DUMMY') = 'OSIF_COUNTER_SYSTEM'" true="System Timer is not enabled in OsIf (OsIfGeneral/OsIfUseSystemTimer checkbox)"/>
                  </a:da>
                  <a:da name="RANGE">
                    <a:v>OSIF_COUNTER_DUMMY</a:v>
                    <a:v>OSIF_COUNTER_SYSTEM</a:v>
                    <a:v>OSIF_COUNTER_CUSTOM</a:v>
                  </a:da>
                </v:var>

                <!-- @implements Adc Timeout Value -->
                <v:var name="AdcTimeoutVal" type="INTEGER">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>The timeout is used for preventing endless loops as resulted from driver FMEA analysis.</p>
                    <p>The hardware failure mode which is preventing, is potential cases with frozen peripheral status used for driver synchronization.</p>
                    <p>The timeout is used as escape for avoidance of endless loops. For more details please refer to driver FMEA.</p></html>]]></a:v>
                  </a:a>
                <a:a name="SCOPE" value="LOCAL"/>
                <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                     type="IMPLEMENTATIONCONFIGCLASS">
                  <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                </a:a>
                  <a:a name="LABEL" value="Adc Timeout Value"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:33cfe352-1233-43a6-b6d2-54239cb433f1b"/>
                  <a:da name="DEFAULT" value="100000"/>
                  <a:da name="INVALID" type="Range">
                    <a:tst expr="&lt;=4294967295" />
                    <a:tst expr="&gt;=0" />
                  </a:da>
                </v:var>

                 <!-- @implements AdcSarIpDevErrorDetect_Object -->
                <v:var name="AdcSarIpDevErrorDetect" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter Enables / Disables development error detection for Adc Sar.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Sar Dev Error Detect"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-155e3870a2ab" />
                  <a:da name="DEFAULT" value="false" />
                </v:var>

                <!-- @implements CtuIpDevErrorDetect_Object -->
                <v:var name="CtuIpDevErrorDetect" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Enables / Disables development error detection for Ctu.
                          This feature is enabled if Adc CTU Control Mode API from General/AutosarExt is enabled or if Adc Hw Trigger from General is enabled or when at least one Adc Group from at least one Hw Unit has Adc Group Trigger Source set as ADC_TRIGG_SRC_HW and Adc Group Hw Trigger Source different from EXT_TRIG.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Ctu Dev Error Detect"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4730-9e80-145e3870a2ab" />
                  <a:da name="DEFAULT" value="false" />
                  <a:a name="EDITABLE" type="XPath">
                    <a:tst expr="(../AdcEnableCtuControlModeApi = 'true') or (../../AdcGeneral/AdcHwTriggerApi = 'true')"/>
                  </a:a>
                </v:var>

                <!-- @implements AdcMulticoreSupport_Object -->
                <v:var name="AdcMulticoreSupport" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter globally enables the possibility to support multicore. If this parameter is enabled, at least one EcucPartition needs to be defined (in all variants).<br>
                  <h1>Note</h1>This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Multicore Support"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2ab" />
                  <a:da name="DEFAULT" value="false" />
                  <a:a name="EDITABLE" value="true"/>
                  <a:a name="READONLY" value="false"/>
                </v:var>

                <!-- CPR_RTD_00046.adc -->
                <!-- @implements AdcEnableGroupDependentChannelNames_Object -->
                <v:var name="AdcEnableGroupDependentChannelNames" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This is used to generate ADC symbolic names, that depend also on the ADC group
                        to which each ADC channel is mapped. The generated symbolic name will be something like:
                        #define "ADC_GroupName"_"ADC_ChannelName"  "Channel index value",
                        where "Channel index value" is the channel index in the current group.
                        Channel indexes in each group are generated to allow result buffer access by symbolic names.</p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Channel Indexes Symbolic Names"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:a80d600d-8395-4bf7-b5d6-3a1b3c3ed2de" />
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <v:var name="AdcMaxPartitions" type="INTEGER">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter limits the number of EcucPartitions defined in all variants. This parameter is enabled if Adc Multicore Support from AutosarExt is enabled.<br>
                  <h1>Note</h1>This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Maximum partitions number"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a3bc" />
                  <a:da name="DEFAULT" value="1" />
                  <a:a name="EDITABLE" type="XPath">
                    <a:tst expr="../AdcMulticoreSupport = 'true'"/>
                  </a:a>
                  <a:a name="READONLY" value="false"/>
                </v:var>

                <!-- AdcBypassAbortChainCheck -->
                <v:var name="AdcBypassAbortChainCheck" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Bypass the delay introduced to check if an aborted conversion chain has stopped.
                      This increases ADC driver performance at the cost of HW-SW coherency no longer being guaranteed.
                      The user must make sure he does not call an ADC service before the hardware reaches the correct state.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Bypass Abort Chain Check"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:e3ea8f83-9cdb-465c-a9d1-6274851fc99a" />
                  <a:da name="DEFAULT" value="false"/>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="node:fallback(.,'true') = 'true' and node:fallback(../../AdcGeneral/AdcPriorityImplementation, 'ADC_PRIORITY_HW_SW') = 'ADC_PRIORITY_HW_SW'"
                    true="AdcBypassAbortChainCheck parameter can be true if and only if there is NO priority."/>
                    <a:tst expr="node:fallback(.,'true') = 'true' and node:fallback(../../AdcGeneral/AdcEnableQueuing, 'true') = 'true'"
                    true="AdcBypassAbortChainCheck parameter can be true if and only if there is NO queue."/>
                  </a:da>
                </v:var>

                <!-- CPR-MCAL-782.adc -->
                <!-- @implements AdcConvTimeOnce_Object -->
                <v:var name="AdcConvTimeOnce" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v>
                    <![CDATA[EN:<html><p> Implementation Specific Parameter.
                         Enable/Disable one time setting of the registers.
                         If Enabled, the setting of the conversion time registers will be done only once in Adc_Init() function
                        for the configured hardware unit.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Conversion Time Once"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:6d78d92b-c387-4656-ba8c-8113fdef7242"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="(.= 'true') and (num:i(count(../../AdcConfigSet/AdcHwUnit/*)) > num:i(count(../../AdcConfigSet/AdcHwUnit/*/AdcNormalConvTimings)))"
                    true="AdcNormalConvTimings for all AdcHwUnits must be configured when Adc Conversion Time Once is enabled."/>
                  </a:da>
                </v:var>

                <!-- CPR-MCAL-799.adc -->
                <!--  @implements AdcOptimizeOneShotHwTriggerConversions_Object  -->
                <v:var name="AdcOptimizeOneShotHwTriggerConversions" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v>
                    <![CDATA[EN:<html><p>Implementation Specific Parameter.
                         Enable/Disable The Adc driver optimization for HW Triggered groups, OneShot, Single access.
                         If Enabled, other types of groups cannot be configured in ADC driver and the code for interrupt routine / Dma notification will be optimized for speed.
                         Also, all groups must have at most 8 channels configured.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Optimize OneShot HwTrigger Conversions"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:6d78d92b-c387-4656-ba8c-8113fdef7243"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:a name="ORIGIN" value="NXP"/>
                </v:var>

                <!-- CPR-MCAL-859.adc -->
                <!--  @implements AdcOptimizeDmaStreamingGroups_Object  -->
                <v:var name="AdcOptimizeDmaStreamingGroups" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v>
                    <![CDATA[EN:<html><p> Implementation Specific Parameter.
                                Enable/Disable The Adc driver enable Optimize DMA streaming groups for reducing the number of interrupts required for processing the conversions of Adc Groups that consist of one or more channels (depending on HW capabilities) and which are configured as ADC_ACCESS_MODE_STREAMING.
                                When this feature is enabled, only one interrupt will be raised after the completion of all stream conversions (as configured by AdcStreamingNumSamples parameter). An additional interrupt to be raised after half of the stream is converted shall also be configurable.
                                This feature is enabled if Adc Global Enable DMA Transfer from General/AutosarExt is also enabled.
                                Note:
                                    - SetChannel(), Enable/DisableChannel() and Optimize one-shot hardware trigger cannot be use concurrently with this feature.
                                    This node is EDITABLE only if "Adc Enable DMA support" is enabled</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Optimize DMA Streaming Groups"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:6d78d92b-c387-4656-ba8c-8113fdef7247"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="EDITABLE" type="XPath">
                    <a:tst expr="../AdcEnableDmaTransferMode ='true'"/>
                  </a:a>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="(.= 'true') and (../AdcOptimizeOneShotHwTriggerConversions = 'true')"
                    true="AdcOptimizeDmaStreamingGroups and AdcOptimizeOneShotHwTriggerConversions cannot be enabled simultaneous."/>
                    <a:tst expr="(.= 'true') and (../AdcEnableDmaTransferMode = 'false')"
                    true="AdcOptimizeDmaStreamingGroups can be enabled only if AdcEnableDmaTransferMode is available."/>
                  </a:da>
                </v:var>

                <!-- CPR-MCAL-782.adc -->
                <!-- AdcPreSamplingOnce -->
                <v:var name="AdcPreSamplingOnce" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v>
                    <![CDATA[EN:<html><p>Implementation Specific Parameter.
                        Enable/Disable one time setting of the registers.
                        If Enabled, the setting of the presampling time registers will be done only once in Adc_Init() function
                        for the configured hardware unit.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Presampling Time Once"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:61caf85d-c82e-4ae6-a6a8-712e72f21dc2"/>
                  <a:da name="DEFAULT" value="true"/>
                  <a:da name="READONLY" value="true"/>
                  <a:a name="ORIGIN" value="NXP"/>
                </v:var>

                <!-- CPR-MCAL-796.adc -->
                <!-- AdcEnableInitialNotification -->
                <v:var name="AdcEnableInitialNotification" type="BOOLEAN">
                <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Enable/disable an extra notification to be called for each Adc Group conversion.
                          This feature is intended to be used together with Adc_SetChannel service. The initial notification can be used by the user application to call Adc_SetChannel API before ADC driver updates the hardware configuration for the next conversion.
                          This node is EDITABLE only if "Adc Set Channel API" is enabled.</p></html>]]>
                    </a:v>
                  </a:a>
                <a:a name="IMPLEMENTATIONCONFIGCLASS"
                    type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                </a:a>
                <a:a name="LABEL" value="Adc Initial Notification Capability"/>
                <a:a name="ORIGIN" value="NXP"/>
                <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                <a:a name="UUID" value="ECUC:e06b25fe-9949-46fe-b350-3deb0f8d5679"/>
                <a:da name="DEFAULT" value="false"/>
                <a:da name="EDITABLE" type="XPath" expr="node:fallback(../AdcEnableSetChannel,'false') = 'true'"/>
                <a:da name="INVALID" type="XPath">
                    <a:tst expr="(. = 'true') and (node:fallback(../AdcEnableSetChannel,'false') = 'false')"
                    true="AdcEnableInitialNotification can only be enabled if AdcEnableSetChannel is also enabled"/>
                </a:da>
                </v:var>

                <v:var name="AdcEnableDmaTransferMode" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter enables the possibility to configure DMA transfer for ADC converted data. If this parameter is disabled then DMA handling code will be removed at pre-compile time and DMA transfer cannot be configure for any Adc unit in any variant. If this parameter is enabled then the DMA configuration code will not be removed.<br>
                  <h1>Note</h1> :This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable DMA support"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2cb" />
                  <a:da name="DEFAULT" value="false" />
                  <a:a name="INVALID" type="XPath">
                     <a:tst expr="(. = 'true') and ((../AdcEnableSetChannel = 'true') or (../AdcEnableChDisableChApi = 'true'))"
                           true="AdcEnableDmaTransferMode shouldn't be configured simultaneously with Adc_SetChannel or Adc_EnableChannel/Adc_DisableChannel because of the risk of modification DMA TCD size at runtime"/>
                  </a:a>
                </v:var>

                <v:var name="AdcEnableDmaErrorNotification" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter enables the possibility to detect DMA transfer error for ADC converted data. If this parameter is disabled then DMA detecting error code will be removed at pre-compile time. If this parameter is enabled then the DMA detecting error code will not be removed.<br>
                  <h1>Note</h1> :This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Dma Error Enable"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:f1a5fd83-5aaf-47ce-847f-d2470b09bdb5" />
                  <a:da name="DEFAULT" value="false" />
                  <a:a name="INVALID" type="XPath">
                     <a:tst expr="(../AdcEnableDmaTransferMode = 'false') and (. = 'true')"
                           true="AdcEnableDmaErrorNotification must be used in DMA mode"/>
                  </a:a>
                </v:var>

                <v:var name="AdcUseSoftwareInjectedGroups" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter defines if Software Injected Groups are used in any Hardware Unit, any variant.
                    It needs to be enabled if Software Injected Groups are needed. If Software Injected Groups are not needed, this parameter should be disabled for code optimizations.<br>
                    <h1>Note</h1>This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Use Software Injected Groups"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a212" />
                  <a:a name="INVALID" type="XPath">
                    <a:tst expr="(. = 'true') and (../../AdcGeneral/AdcEnableStartStopGroupApi = 'false')"
                           true="AdcEnableStartStopGroupApi has to be enabled before using Software Injected Groups."/>
                  </a:a>
                  <a:da name="DEFAULT" value="false" />
                </v:var>


                <v:var name="AdcUseHardwareNormalGroups" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter defines if Hardware Normal Groups are used in any Hardware Unit, any variant. It needs to be enabled if Hardware Normal Groups are needed. If Hardware Normal Groups are not needed, this parameter should be disabled for code optimizations.<br>
                        <h1>Note</h1>This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Use Hardware Normal Groups"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a213" />
                  <a:da name="DEFAULT" value="false" />
                </v:var>

                <!-- CPR-MCAL-825.adc -->
                <!--  @implements AdcEnableUserModeSupport_Object  -->
                <v:var name="AdcEnableUserModeSupport" type="BOOLEAN">
                    <a:a name="DESC">
                        <a:v>
                            <![CDATA[EN:<html>
                                When this parameter is enabled, the Adc module will adapt to run from User Mode, by configuring REG_PROT for ADC IPs <p/>
                                Note: The Adc driver code can be executed at any time from both supervisor and user mode.
                            </html>]]>
                        </a:v>
                    </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                    <a:a name="LABEL" value="Enable Adc User Mode Support"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:6d78d92b-c387-4656-ba8c-8113fdef7061"/>
                    <a:da name="DEFAULT" value="false"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="EDITABLE" type="XPath" expr="(ecu:get(&apos;AdcAdcSarRegProtAvailable&apos;) = 'TRUE') or (ecu:get(&apos;AdcCtuRegProtAvailable&apos;) = 'TRUE')"/>
                </v:var>

                <!-- PR-MCAL-3187.adc -->
                <!-- @implements AdcSetHwUnitPowerModeApi_Object -->
                <v:var name="AdcSetHwUnitPowerModeApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>Adds/removes the Autosar Extension implementation API Adc_SetHwUnitPowerMode() from the code.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Set HW Unit Power Mode API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:100169f9-9894-40a7-ac10-a9b20a7a322e"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- PR-MCAL-3233.adc -->
                <!-- AdcEnableChDisableChApi -->
                <v:var name="AdcEnableChDisableChApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          Enable/disable the Autosar Extension implementation api(s) Adc_EnableChannel() and Adc_DisableChannel() in ADC driver.<br>
                          <h1>Note</h1>: This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable/Disable Channels API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:4255ca68-6347-4467-9db6-d5782e5c3836"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:da name="INVALID" type="XPath">
                     <a:tst expr="(. = 'true') and (../AdcEnableSetChannel = 'true')"
                     true="AdcEnableChDisableChApi and AdcEnableSetChannel cannot be enabled simultaneously"/>
                  </a:da>
                </v:var>

                <!-- AdcGetInjectedConvStatusApi -->
                <v:var name="AdcGetInjectedConvStatusApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          Enable/disable the Autosar Extension API Adc_GetInjectedConversionStatus() in ADC driver.<br>
                          <h1>Note</h1>This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Get Injected Conversions Status API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:b1f7f392-1e4a-46a1-876a-e30a3d9e81dd"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- CPR_RTD_00033.adc -->
                <!--  @implements AdcEnableThresholdConfiguration_Object  -->
                <v:var name="AdcEnableThresholdConfigurationApi" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          Enable/disable the Autosar Extension APIs Adc_ConfigureThreshold() in ADC driver.<br>
                          <h1>Note</h1>: This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Configure Threshold API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:fd1abc35-f49b-449f-94f5-cb6289d09ee7"/>
                  <a:a name="INVALID" type="XPath">
                  <a:tst expr="(../AdcEnableWatchdogApi ='false' and .='true')"
                           true ="AdcEnableWatchdogApi must be enabled in order to use the Adc_ConfigureThreshold functionality."/>
                  </a:a>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- CPR_RTD_00037, CPR_RTD_00039, CPR_RTD_00041 -->
                <!-- @implements AdcEnableCtuTrigAutosarExtApi_Object -->
                <v:var name="AdcEnableCtuTrigAutosarExtApi" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This is used to enable the Autosar Extension API for the hardware triggered group.
                        If this parameter is enabled then Adc_EnableCTUTrigger(), Adc_DisableCTUTrigger() and Adc_HwResultReadGroup() will be available in the driver code.
                        This is an Implementation Specific Parameter.
                        When this parameter is enabled, the result buffer is no longer to be used to read the results as the result will be directly read from HW registers.
                        When this parameter is disabled, normal functionality shall be executed.
                        This node is EDITABLE only if "Adc Hw Trigger API" is enabled</p></html>]]></a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc CTU Hardware Trigger API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2ba" />
                  <a:da name="DEFAULT" value="false" />
                  <a:da name="EDITABLE" value="true"/>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="(. = 'true') and (node:fallback(../../AdcGeneral/AdcHwTriggerApi,'false') = 'false')"
                           true="This parameter can be true only if Adc/AdcGeneral/AdcHwTriggerApi is enabled"/>
                  </a:da>
                </v:var>

                <!--  @implements AdcCtuHardwareTriggerOptimization_Object  -->
                <v:var name="AdcCtuHardwareTriggerOptimization" type="BOOLEAN">
                    <a:a name="DESC">
                        <a:v>
                            <![CDATA[EN:<html>
                                When this parameter is enabled, the Ctu channel lists and triggers are configured only once at initialization in order to reduce cpu load when calling the Adc_EnableHardwareTrigger and Adc_EnableCtuTrigger APIs.
                                The Adc_SetChannel service cannot be used, and the maximum size of groups cannot exceed the size of the entire command list divided by the number of adc hardware units triggered by the CTU/BCTU.
                                This node is EDITABLE only if "Adc Hw Trigger API" is enabled
                            </html>]]>
                        </a:v>
                    </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                    <a:a name="LABEL" value="CTU Enable Hardware Trigger Optimization"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:a8437e4d-123e-4fdd-9d3f-a109g2905df3"/>
                    <a:da name="DEFAULT" value="false"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:da name="INVALID" type="XPath">
                      <a:tst expr="(. = 'true') and (node:fallback(../../AdcGeneral/AdcHwTriggerApi,'false') = 'false')"
                            true="This parameter can be true only if Adc/AdcGeneral/AdcHwTriggerApi is enabled"/>
                    </a:da>
                </v:var>

                 <!-- @implements AdcEnableCtuControlModeApi_Object -->
                <v:var name="AdcEnableCtuControlModeApi" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This is used to enable the Autosar Extension API for the enabling and disabling CTU control mode for an ADC unit.
                        If this parameter is enabled than Adc_EnableCtuControlMode(), Adc_DisableCtuControlMode() will be available in the driver code.
                        When a unit works in CTU control mode, no other conversions shall run in parallel(Adc). The only conversions occurring shall be the ones defined in the CTU configuration.
                        If AdcEnableCtuControlModeApi is enabled, CTU must be configured.<br>
                        <h1>Note</h1>This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc CTU Control Mode API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2ca" />
                  <a:da name="DEFAULT" value="false" />
                  <a:a name="EDITABLE" value="true"/>
                  <a:a name="INVALID" type="XPath">
                     <!-- Checking Mcl is configured or not? -->
                  </a:a>
                </v:var>

                <v:var name="CtuEnableDmaTransferMode" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter enables the possibility to configure DMA transfer for CTU control mode.<br>
                        If this parameter is disabled then DMA handling code will be removed at pre-compile time and DMA transfer cannot be configured for any CTU unit in any variant.<br>
                        If this parameter is enabled then the DMA configuration code will not be removed.<br>
                        <h1>Note</h1>: This is an Implementation Specific Parameter.
                        This node is EDITABLE only if "Adc CTU Control Mode API" is enabled
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc CTU Control Mode Enable DMA support"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2bc" />
                  <a:da name="DEFAULT" value="false" />
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="(. = 'true') and (node:fallback(../AdcEnableCtuControlModeApi,'false') = 'false')"
                           true="This parameter can be true only if AdcEnableCtuControlModeApi is enabled"/>
                  </a:da>
                </v:var>

                <!-- Adc Ctu Control Mode Extra Apis -->
                <v:var name="AdcCtuControlModeExtraApis" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>The option provides some extra APIs for Ctu Control Mode: Adc_CtuWriteTriggerEnableMask, Adc_CtuSetTriggerEnable, Adc_CtuSetTriggerAdcCmdAddress and Adc_CtuSetTriggerCompare.
                          These APIs are used to configure Trigger Handler Control Register (THCR), Command List Control Register (CLCR) and Trigger Compare Register (TxCR).</p>
                          <h1>Note:</h1> <p>This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable CTU Mode Extra API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:1726179a-3157-4aee-9521-3aa672b78662"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:a name="INVALID" type="XPath">
                    <a:tst expr="(../AdcEnableCtuControlModeApi ='false' and .='true')"
                           true ="AdcCtuControlModeExtraApis must be disabled when AdcEnableCtuControlModeApi is false."/>
                  </a:a>
                </v:var>

                <v:var name="AdcEnableWatchdogApi" type="BOOLEAN">
                  <a:a name="DESC">
                  <a:v><![CDATA[EN:<html><p>This parameter globally enables the possibility to use the Adc Watchdog feature. If this parameter is disabled, the Watchdog handling code will be removed at pre-compile time and nothing related to this functionality can be configured in any unit, for anu variant. If this parameter is enabled, Analog Watchdog functionality can be configured.<br>
                        <h1>Note</h1>: This is an Implementation Specific Parameter.
                        </p></html>]]></a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Watchdog Api"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false" />
                  <a:a name="UUID" value="ECUC:8436de9b-c7e1-4720-9e80-145e3870a2cc" />
                  <a:da name="DEFAULT" value="false" />
                </v:var>

                <!-- @implements AdcEnableSetChannel_Object -->
                <v:var name="AdcEnableSetChannel" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          If this parameter has been configured to &quot;TRUE&quot;, the Autosar Extension function &quot;Adc_SetChannel()&quot;  shall be accessible, otherwise this function shall be removed from the code.
                          <h1>Note</h1>: This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Set Channel API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:00118f07-32be-429d-8b41-3f7dc39d1849"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:da name="INVALID" type="XPath">
                     <a:tst expr="(. = 'true') and (../AdcEnableChDisableChApi = 'true')"
                     true="AdcEnableChDisableChApi and AdcEnableSetChannel cannot be enabled simultaneously"/>
                  </a:da>
                </v:var>

                <!-- AdcEnableDualClockMode -->
                <v:var name="AdcEnableDualClockMode" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[<html><p>Adds/removes the Dual Clock mode service Adc_SetClockMode from the code.
                          Also it enables the Programming of Conversion Timing registers in Adc_SetClockMode.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Set Clock Mode API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:100169f9-9894-40a7-ac10-a9b20a7a3222"/>
                  <a:da name="INVALID" type="XPath">
                    <a:tst expr="(.= 'true') and (../AdcConvTimeOnce = 'true') and (num:i(count(../../AdcConfigSet/AdcHwUnit/*)) > num:i(count(../../AdcConfigSet/AdcHwUnit/*/AdcAlternateConvTimings)))"
                    true="AdcAlternateConvTimings for all AdcHwUnits must be configured when Adc Conversion Time Once and Adc Set Clock Mode API are enabled."/>
                  </a:da>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- @implements AdcEnableCalibration_Object -->
                <v:var name="AdcEnableCalibration" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          If this parameter has been configured to &quot;TRUE&quot;, the Autosar Extension function &quot;Adc_Calibrate()&quot;  shall be accessible, otherwise this function shall be removed from the code.
                          <h1>Note</h1> This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Calibration API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:00118f07-32be-429d-8b41-3f7dc39d1848"/>
                  <a:da name="DEFAULT" value="false"/>
                </v:var>

                <!-- @implements AdcEnableAsyncCalibration_Object -->
                <v:var name="AdcEnableAsyncCalibration" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          Vendor specific: If this parameter has been configured to TRUE&quot; Adc_Calibration will be used in asynchronous mode
                          <h1>Note</h1> This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Asynchronous Calibration"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:92a58b7f-9e14-4476-870a-5bfaa05f93d4"/>
                  <a:da name="DEFAULT" value="false"/>
                <a:da name="EDITABLE" type="XPath" expr="node:fallback(../AdcEnableCalibration,'false') = 'true'"/>
                <a:da name="INVALID" type="XPath">
                    <a:tst expr="(. = 'true') and (node:fallback(../AdcEnableCalibration,'false') = 'false')"
                    true="This parameter can only be enabled if AdcEnableCalibration is also enabled"/>
                </a:da>
                </v:var>

                <!-- CPR-MCAL-858.adc -->
                <!-- @implements AdcEnableSelfTest_Object -->
                <v:var name="AdcEnableSelfTest" type="BOOLEAN">
                  <a:a name="DESC">
                    <a:v><![CDATA[EN:<html><p>
                          If this parameter has been configured to &quot;TRUE&quot;, the Autosar Extension function &quot;Adc_SelfTest()&quot;  shall be accessible, otherwise this function shall be removed from the code.
                          <h1>Note</h1>This is an Implementation Specific Parameter.</p></html>]]>
                    </a:v>
                  </a:a>
                  <a:a name="SCOPE" value="LOCAL"/>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS" type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                  </a:a>
                  <a:a name="LABEL" value="Adc Enable Self Test API"/>
                  <a:a name="ORIGIN" value="NXP"/>
                  <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                  <a:a name="UUID" value="ECUC:00118f07-32be-429d-8b41-3f7dc90d1113"/>
                  <a:da name="DEFAULT" value="false"/>
                  <a:a name="READONLY" value="false"/>
                </v:var>

                <!-- CPR_RTD_00508 -->
                <!--  @implements AdcEnableReadRawDataApi_Object  -->
                <v:var name="AdcEnableReadRawDataApi" type="BOOLEAN">
                    <a:a name="DESC">
                        <a:v>
                            <![CDATA[EN:<html>
                                When this parameter is enabled, the Api for reading the raw result data from an ADC unit is available to use at runtime.
                            </html>]]>
                        </a:v>
                    </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                    <a:a name="LABEL" value="Adc Enable Raw Data Read Api"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:6d78d92b-c387-4656-ba8c-8113fdef7062"/>
                    <a:da name="DEFAULT" value="false"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="READONLY" value="false"/>
                    <a:a name="EDITABLE" type="XPath">
                      <a:tst expr="(../AdcEnableCtuControlModeApi = 'true')"/>
                    </a:a>
                </v:var>

                <!--  @implements AdcEnableGroupStreamingResultReorder_Object  -->
                <v:var name="AdcEnableGroupStreamingResultReorder" type="BOOLEAN">
                    <a:a name="DESC">
                        <a:v>
                            <![CDATA[EN:<html>
                                When this parameter is enabled, the adc results can be arranged as multiple sets of group result buffer if AdcStreamResultGroup is enabled for that selected group.
                                E.g: for a group with channels {CH1 CH5 CH7} the resulting stream buffer shall be:
                                { CH1, CH5, CH7, CH1, CH5, CH7, CH1, CH5, CH7}
                                instead of
                                { CH1, CH1, CH1, CH5, CH5, CH5, CH7, CH7, CH7} like supported by AUTOSAR standard.
                                Apply only for ADC_ACCESS_MODE_STREAMING Access Mode.
                            </html>]]>
                        </a:v>
                    </a:a>
                  <a:a name="POSTBUILDVARIANTVALUE" value="false"/>
                  <a:a name="IMPLEMENTATIONCONFIGCLASS"
                       type="IMPLEMENTATIONCONFIGCLASS">
                    <icc:v vclass="PreCompile">VariantPostBuild</icc:v>
                    <icc:v vclass="PreCompile">VariantPreCompile</icc:v>
                  </a:a>
                    <a:a name="LABEL" value="Adc Enable Group Streaming Results Reorder"/>
                    <a:a name="SYMBOLICNAMEVALUE" value="false"/>
                    <a:a name="UUID" value="ECUC:a1598h2c-123e-4fed-9d3f-d108d0408df3"/>
                    <a:da name="DEFAULT" value="false"/>
                    <a:a name="ORIGIN" value="NXP"/>
                    <a:a name="READONLY" value="false"/>
                </v:var>
              </v:ctr>

              <d:ref type="REFINED_MODULE_DEF" value="ASPath:/AUTOSAR/EcucDefs/Adc"/>
            </v:ctr>
          </d:chc>
          <d:chc name="Adc_EcuParameterDefinition"
                 type="AR-ELEMENT" value="ECU_PARAMETER_DEFINITION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="UUID" value="ECUC:ef7a4f21-7901-4f1f-a2e2-81498a0b7b8f"/>
              <a:a name="DEF"
                   value="ASPath:/AR_PACKAGE_SCHEMA/ECU_PARAMETER_DEFINITION"/>
              <d:lst name="MODULE_REF">
                <d:ref type="MODULE_REF" value="ASPath:/TS_T40D11M50I0R0/Adc"/>
              </d:lst>
            </d:ctr>
          </d:chc>
          <d:chc name="Adc_ModuleDescription"
                 type="AR-ELEMENT" value="BSW_MODULE_DESCRIPTION">
            <d:ctr type="AR-ELEMENT">
              <a:a name="DEF"
                   value="ASPath:/AR_PACKAGE_SCHEMA/BSW_MODULE_DESCRIPTION"/>
              <d:var name="MODULE_ID" type="INTEGER" >
                <a:a name="EDITABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ref type="RECOMMENDED_CONFIGURATION" >
                <a:a name="EDITABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="PRE_CONFIGURED_CONF" >
                <a:a name="EDITABLE" value="false"/>
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:ref>
              <d:ref type="VENDOR_SPECIFIC_MODULE_DEF"
                     value="ASPath:/TS_T40D11M50I0R0/Adc"/>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>

