<?xml version="1.0" encoding= "UTF-8" ?>
<configuration name="S32R45" xsi:schemaLocation="http://mcuxpresso.nxp.com/XSD/mex_configuration_15 http://mcuxpresso.nxp.com/XSD/mex_configuration_15.xsd" uuid="1afd9650-62c5-4fdc-8d79-839018a3a5d5" version="15" xmlns="http://mcuxpresso.nxp.com/XSD/mex_configuration_15" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <common>
      <processor>S32R45</processor>
      <package>S32R45_780bga</package>
      <mcu_data>$(release_id)</mcu_data>
      <cores selected="M7_0">
         <core name="Cortex-A53 (Core #0)" id="A53_0_0" description=""/>
         <core name="Cortex-A53 (Core #1)" id="A53_0_1" description=""/>
         <core name="Cortex-A53 (Core #2)" id="A53_1_0" description=""/>
         <core name="Cortex-A53 (Core #3)" id="A53_1_1" description=""/>
         <core name="Cortex-M7 (Core #4)" id="M7_0" description=""/>
         <core name="Cortex-M7 (Core #5)" id="M7_1" description=""/>
         <core name="Cortex-M7 (Core #6)" id="M7_2" description=""/>
      </cores>
      <description></description>
   </common>
   <preferences>
      <validate_boot_init_only>true</validate_boot_init_only>
      <generate_extended_information>false</generate_extended_information>
      <generate_code_modified_registers_only>false</generate_code_modified_registers_only>
      <update_include_paths>true</update_include_paths>
      <generate_registers_defines>false</generate_registers_defines>
   </preferences>
   <tools>
      <pins name="Pins" version="13.1" enabled="false" update_project_code="true">
         <generated_project_files/>
         <pins_profile>
            <processor_version>0.0.0</processor_version>
            <power_domains/>
         </pins_profile>
         <functions_list>
            <function name="BOARD_InitPins">
               <description>Configures pin routing and optionally pin electrical features.</description>
               <options>
                  <callFromInitBoot>true</callFromInitBoot>
                  <coreID>M7_0</coreID>
               </options>
               <dependencies/>
               <pins/>
            </function>
         </functions_list>
      </pins>
      <clocks name="Clocks" version="13.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="generate/include/Clock_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_Cfg_Defines.h" update_enabled="true"/>
            <file path="generate/include/Clock_Ip_PBcfg.h" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/Clock_Ip_PBcfg.c" update_enabled="true"/>
         </generated_project_files>
         <clocks_profile>
            <processor_version>0.0.0</processor_version>
         </clocks_profile>
         <clock_configurations>
            <clock_configuration name="BOARD_BootClockRUN" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.EXTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.EXTAL, Clocks tool id: FXOSC_CLK.EXTAL) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="FXOSC_CLK.XTAL" description="&apos;External pin&apos; (Pins tool id: FXOSC_CLK.XTAL, Clocks tool id: FXOSC_CLK.XTAL) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_0_EXT_REF, Clocks tool id: external_clocks.FTM_0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".FTM_1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .FTM_1_EXT_REF, Clocks tool id: external_clocks.FTM_1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_REF, Clocks tool id: external_clocks.GMAC0_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_REF, Clocks tool id: external_clocks.GMAC0_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_RX_REF, Clocks tool id: external_clocks.GMAC0_EXT_RX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_RX_REF, Clocks tool id: external_clocks.GMAC0_EXT_RX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_TX_REF, Clocks tool id: external_clocks.GMAC0_EXT_TX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC0_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC0_EXT_TX_REF, Clocks tool id: external_clocks.GMAC0_EXT_TX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_REF, Clocks tool id: external_clocks.GMAC1_EXT_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_REF, Clocks tool id: external_clocks.GMAC1_EXT_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_RX_REF, Clocks tool id: external_clocks.GMAC1_EXT_RX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_RX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_RX_REF, Clocks tool id: external_clocks.GMAC1_EXT_RX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_TX_REF, Clocks tool id: external_clocks.GMAC1_EXT_TX_REF) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC1_EXT_TX_REF" description="&apos;External pin&apos; (Pins tool id: .GMAC1_EXT_TX_REF, Clocks tool id: external_clocks.GMAC1_EXT_TX_REF) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS, Clocks tool id: external_clocks.GMAC_EXT_TS) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId=".GMAC_EXT_TS" description="&apos;External pin&apos; (Pins tool id: .GMAC_EXT_TS, Clocks tool id: external_clocks.GMAC_EXT_TS) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="A53_0_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="A53_1_1">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="M7_0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="M7_2">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.driver.clock" description="Clocks initialization requires the CLOCK Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="M7_1">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="FXOSC_CLK.FXOSC_CLK.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_0_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.ftm_1_ext_ref.outFreq" value="20 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac0_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac0_ext_rx_ref.outFreq" value="25 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac0_ext_tx_ref.outFreq" value="25 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac1_ext_ref.outFreq" value="40 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac1_ext_rx_ref.outFreq" value="25 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac1_ext_tx_ref.outFreq" value="25 MHz" locked="false" enabled="true"/>
                  <clock_source id="external_clocks.gmac_ext_ts.outFreq" value="20 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="A53_CORE_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV10_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="A53_CORE_DIV2_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL3_DIV3_CLK.outFreq" value="16 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ACCEL_PLL_PHI0.outFreq" value="600 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="ADC1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT0_CLK.outFreq" value="40/13 MHz" locked="false" accuracy=""/>
                  <clock_output id="CLKOUT1_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS1.outFreq" value="640 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS2.outFreq" value="640 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS3.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS4.outFreq" value="240 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS5.outFreq" value="480 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_DFS6.outFreq" value="480 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI0.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="CORE_PLL_PHI1.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="CRC0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="CTU0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR0_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_CLK.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DDR_PLL_PHI0.outFreq" value="400 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA0_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA1_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX2_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMAMUX3_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC0_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="DMA_CRC1_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM0_CLK.outFreq" value="4.8 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM1_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM2_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM3_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_BBE32DSP_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_LAX0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_LAX1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="EIM_PER1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_CPU0_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_CPU1_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_CPU2_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_EDMA0_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_EDMA1_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_LAX0_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_LAX1_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_PER1_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERM_PER_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="FDMA0_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="FIRCOUT.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN7_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXCAN_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXRAY_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERA_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FLEXTIMERB_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FRAY0_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTIMER1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_0_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FTM_1_EXT_REF.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="FXOSCOUT.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_EXT_RX_REF.outFreq" value="25 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_EXT_TX_REF.outFreq" value="25 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC0_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_EXT_REF.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_EXT_RX_REF.outFreq" value="25 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_EXT_TX_REF.outFreq" value="25 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_REF_DIV_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_RX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC1_TX_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_EXT_TS.outFreq" value="20 MHz" locked="false" accuracy=""/>
                  <clock_output id="GMAC_TS_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="IIC1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST0_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST1_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST2_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST3_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST4_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST5_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST6_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST7_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LBIST_CLK.outFreq" value="40 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LINFLEXD_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="LIN_BAUD_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="OCOTP0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS1.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS2.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS3.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS4.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS5.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_DFS6.outFreq" value="500 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI0.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI1.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI2.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI3.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI4.outFreq" value="200 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI5.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI6.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PERIPH_PLL_PHI7.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="PER_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="PIT1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_1X_CLK.outFreq" value="24 MHz" locked="false" accuracy=""/>
                  <clock_output id="QSPI_2X_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SDHC_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_LANE_0_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_LANE_0_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_LANE_1_CDR.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_0_LANE_1_TX.outFreq" value="100 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_LANE_0_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_LANE_0_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_LANE_1_CDR.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SERDES_1_LANE_1_TX.outFreq" value="125 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIRCOUT.outFreq" value="32 kHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SIUL1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SPI_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM0_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM1_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM2_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM3_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM4_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM5_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM6_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="STM7_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT1_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT2_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT3_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT4_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT5_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="SWT6_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="USDHC0_CLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="WKPU0_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_2X_CLK.outFreq" value="640 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_CLK.outFreq" value="320 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV2_CLK.outFreq" value="160 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV3_CLK.outFreq" value="320/3 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV4_CLK.outFreq" value="80 MHz" locked="false" accuracy=""/>
                  <clock_output id="XBAR_DIV6_CLK.outFreq" value="160/3 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="ACCELPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="ACCELPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="ACCELPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="ACCEL_MFD.scale" value="60" locked="true"/>
                  <setting id="ACCEL_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="ACCEL_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="COREPLL_DFS1.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS2.scale" value="2.5" locked="true"/>
                  <setting id="COREPLL_DFS3.scale" value="4" locked="true"/>
                  <setting id="COREPLL_DFS4.scale" value="20/3" locked="true"/>
                  <setting id="COREPLL_DFS5.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_DFS6.scale" value="10/3" locked="true"/>
                  <setting id="COREPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="COREPLL_PHI1.scale" value="20" locked="true"/>
                  <setting id="COREPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="COREPLL_PREDIV.scale" value="1" locked="true"/>
                  <setting id="CORE_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_DFS2_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_DFS3_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_DFS4_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_DFS5_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_DFS6_PD" value="Power_up" locked="false"/>
                  <setting id="CORE_MFD.scale" value="40" locked="true"/>
                  <setting id="CORE_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLLODIV_1_DE" value="Enabled" locked="false"/>
                  <setting id="CORE_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DDRPLL_PHI0.scale" value="4" locked="true"/>
                  <setting id="DDRPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="DDR_MFD.scale" value="40" locked="false"/>
                  <setting id="DDR_PLLODIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DDR_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="DIV_0_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_15_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_2_1_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_2_3_DE" value="Enabled" locked="false"/>
                  <setting id="DIV_3_DE" value="Enabled" locked="false"/>
                  <setting id="FXOSC_PM" value="Crystal_mode" locked="false"/>
                  <setting id="MC_CGM_0_MUX_0.sel" value="COREPLL_DFS1" locked="false"/>
                  <setting id="MC_CGM_0_MUX_15_DIV0.scale" value="1" locked="true"/>
                  <setting id="MC_CGM_2_MUX_0_DIV0.scale" value="1" locked="true"/>
                  <setting id="MC_CGM_5_MUX_0.sel" value="DDRPLL_PHI0" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX0_DIV0.scale" value="16" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX0_MUX.sel" value="COREPLL_DFS1" locked="false"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX1_DIV0.scale" value="13" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_0_AUX6_DIV0.scale" value="2" locked="true"/>
                  <setting id="MODULE_CLOCKS.MC_CGM_5_AUX0_MUX.sel" value="DDRPLL_PHI0" locked="false"/>
                  <setting id="PERIPHPLL_DFS1.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS2.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS3.scale" value="10" locked="true"/>
                  <setting id="PERIPHPLL_DFS4.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_DFS5.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_DFS6.scale" value="4" locked="true"/>
                  <setting id="PERIPHPLL_MFD.scale" value="50" locked="true"/>
                  <setting id="PERIPHPLL_PHI0.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI1.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI2.scale" value="25" locked="true"/>
                  <setting id="PERIPHPLL_PHI3.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI4.scale" value="10" locked="true"/>
                  <setting id="PERIPHPLL_PHI5.scale" value="16" locked="true"/>
                  <setting id="PERIPHPLL_PHI6.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PHI7.scale" value="20" locked="true"/>
                  <setting id="PERIPHPLL_PLLCLKMUX.sel" value="FXOSC_CLK.FXOSCOUT" locked="false"/>
                  <setting id="PERIPH_DFS1_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS2_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS3_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS4_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS5_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_DFS6_PD" value="Power_up" locked="false"/>
                  <setting id="PERIPH_PLL_PD" value="Power_up" locked="false"/>
                  <setting id="PLLODIV0_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV1_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV2_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV3_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV4_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV5_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV6_DE" value="Enabled" locked="false"/>
                  <setting id="PLLODIV7_DE" value="Enabled" locked="false"/>
                  <setting id="PREDIV.scale" value="1" locked="true"/>
               </clock_settings>
               <called_from_default_init>true</called_from_default_init>
            </clock_configuration>
         </clock_configurations>
      </clocks>
      <ddr name="DDR" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files/>
         <components/>
      </ddr>
      <dcd name="DCD" version="1.0" enabled="true" update_project_code="true" isSelfTest="false">
         <generated_project_files>
            <file path="board/dcd_config.c" update_enabled="true"/>
         </generated_project_files>
         <dcdx_profile>
            <processor_version>N/A</processor_version>
         </dcdx_profile>
         <dcdx_configurations>
            <dcdx_configuration name="DCD Configuration">
               <description></description>
               <options/>
               <command_groups>
                  <command_group name="DCD Commands" enabled="true">
                     <commands/>
                  </command_group>
               </command_groups>
            </dcdx_configuration>
         </dcdx_configurations>
      </dcd>
      <ivt name="IVT" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/ivt_config.c" update_enabled="true"/>
         </generated_project_files>
         <ivt_profile>
            <processor_version>N/A</processor_version>
         </ivt_profile>
         <ivt_records>
            <ivt_pointers>
               <ivt_pointer id="" index="0" name="Self-Test DCD" size="4" start_address="0x100" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="1" name="Self-Test DCD (backup)" size="4" start_address="0x108" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_self_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="2" name="DCD" size="4" start_address="0x110" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="3" name="DCD (backup)" size="4" start_address="0x118" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_dcd_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="4" name="HSE" size="4" start_address="0x120" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="5" name="HSE (backup)" size="4" start_address="0x128" file_path="N/A" locked="false" reserved="true" sign_image="false">
                  <custom_fields/>
               </ivt_pointer>
               <ivt_pointer id="" index="6" name="Application bootloader" size="4" start_address="0x130" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
               <ivt_pointer id="" index="7" name="Application bootloader (backup)" size="4" start_address="0x138" file_path="N/A" locked="false" reserved="false" sign_image="false">
                  <custom_fields>
                     <custom_field name="gmac_iv_ab_b" value="" disabled="true"/>
                  </custom_fields>
               </ivt_pointer>
            </ivt_pointers>
            <ivt_image start_address="0x0" locked="true" sign_image="false">
               <custom_fields>
                  <custom_field name="gmac_iv_ivt_image" value="" disabled="true"/>
               </custom_fields>
            </ivt_image>
            <automatic_align start_address="0x0"/>
            <struct>
               <struct name="boot_config">
                  <setting>
                     <setting name="secured_boot" value="false"/>
                     <setting name="boot_watchdog" value="false"/>
                     <setting name="boot_target" value="A53_0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="gmac_generation">
                  <setting>
                     <setting name="key_type" value="Plain ADKP"/>
                     <setting name="adkp_file" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="life_cycle_config">
                  <setting>
                     <setting name="life_cycle" value="Keep existing configuration"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="interface_selection">
                  <setting>
                     <setting name="QuadSPI_config_params" value="true"/>
                     <setting name="device_type" value="QuadSPI Serial Flash"/>
                     <setting name="quad_spi_params" value="N/A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="hse_fw_config_parameters_struct">
                  <setting/>
                  <arrays/>
                  <child_structs>
                     <struct name="sys_img_pointer">
                        <setting>
                           <setting name="sys_img_pointer_addr" value="0x81000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_pointer_backup">
                        <setting>
                           <setting name="sys_img_pointer_backup_addr" value="0x8d000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_external_flash_type">
                        <setting>
                           <setting name="external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="sys_img_flash_page_size">
                        <setting>
                           <setting name="flash_page_size" value="0x1000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="app_bsb_external_flash_type">
                        <setting>
                           <setting name="app_external_flash_type" value="QSPI"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_marker">
                        <setting>
                           <setting name="vdd_marker" value="false"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                     <struct name="efuse_vdd_word">
                        <setting>
                           <setting name="io_polarity" value="GPIO low"/>
                           <setting name="gpio_mscr_value" value="0"/>
                           <setting name="delay_in_microseconds" value="1000"/>
                        </setting>
                        <arrays/>
                        <child_structs/>
                     </struct>
                  </child_structs>
               </struct>
            </struct>
            <ivt_flash image_path="" algorithm_name="" port=""/>
         </ivt_records>
      </ivt>
      <quadspi name="QuadSPI" version="1.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/quadspi_config.c" update_enabled="true"/>
         </generated_project_files>
         <quadspi_profile>
            <processor_version>N/A</processor_version>
         </quadspi_profile>
         <quadspi_records>
            <general_settings>
               <struct name="port_connection">
                  <setting>
                     <setting name="port" value="A"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_bypass_mode">
                  <setting>
                     <setting name="dll_bypass_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="dll_auto_upd_mode">
                  <setting>
                     <setting name="dll_auto_upd_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="ipcr_mode">
                  <setting>
                     <setting name="ipcr_trigger_en" value="false"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
               <struct name="sflash_clk_freq">
                  <setting>
                     <setting name="clk_freq" value="0x0"/>
                  </setting>
                  <arrays/>
                  <child_structs/>
               </struct>
            </general_settings>
            <quadspi_register name="MCR" value="0xf404c"/>
            <quadspi_register name="FLSHCR" value="0x303"/>
            <quadspi_register name="BFGENCR" value="0x0"/>
            <quadspi_register name="DLLCRA" value="0x1200000"/>
            <quadspi_register name="PARITYCR" value="0x0"/>
            <quadspi_register name="SFACR" value="0x800"/>
            <quadspi_register name="SMPR" value="0x0"/>
            <quadspi_register name="DLCR" value="0x40ff40ff"/>
            <quadspi_register name="SFA1AD" value="0x0"/>
            <quadspi_register name="SFA2AD" value="0x0"/>
            <quadspi_register name="DLPR" value="0xaa553443"/>
            <quadspi_register name="SFAR" value="0x0"/>
            <quadspi_register name="TBDR" value="0x0"/>
            <data_sequences>
               <struct name="command_sequences">
                  <setting/>
                  <arrays>
                     <array name="lut_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
               <struct name="flash_write_cmd">
                  <setting/>
                  <arrays>
                     <array name="flash_write_cmd_table"/>
                  </arrays>
                  <child_structs/>
               </struct>
            </data_sequences>
         </quadspi_records>
      </quadspi>
      <efuse name="eFUSE" version="1.0" enabled="false" update_project_code="true">
         <efuse_profile>
            <processor_version>N/A</processor_version>
         </efuse_profile>
      </efuse>
      <gtm name="GTM" version="1.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <gtm_profile>
            <processor_version>N/A</processor_version>
         </gtm_profile>
      </gtm>
      <periphs name="Peripherals" version="14.0" enabled="true" update_project_code="true">
         <dependencies>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="osif is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.osif" description="An unsupported version of the osif in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.adc_sar" description="adc_sar is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.adc_sar" description="An unsupported version of the adc_sar in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.ctu" description="ctu is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.ctu" description="An unsupported version of the ctu in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.IntCtrl_Ip" description="IntCtrl_Ip is not found in the toolchain/IDE project. The project will not compile!" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.driver.IntCtrl_Ip" description="An unsupported version of the IntCtrl_Ip in the toolchain/IDE project. Required: ${required_value}, actual: ${actual_value}. The project might not compile correctly." problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">1.0.0</data>
               </feature>
            </dependency>
         </dependencies>
         <generated_project_files>
            <file path="generate/include/Adc_Sar_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Adc_Sar_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Adc_Sar_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/Ctu_Ip_VS_0_PBcfg.h" update_enabled="true"/>
            <file path="generate/include/DeviceDefinition.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_Cfg.h" update_enabled="true"/>
            <file path="generate/include/IntCtrl_Ip_CfgDefines.h" update_enabled="true"/>
            <file path="generate/include/OsIf_ArchCfg.h" update_enabled="true"/>
            <file path="generate/include/OsIf_Cfg.h" update_enabled="true"/>
            <file path="generate/include/Platform_Types.h" update_enabled="true"/>
            <file path="generate/include/Soc_Ips.h" update_enabled="true"/>
            <file path="generate/include/modules.h" update_enabled="true"/>
            <file path="generate/src/Adc_Sar_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/Ctu_Ip_VS_0_PBcfg.c" update_enabled="true"/>
            <file path="generate/src/IntCtrl_Ip_Cfg.c" update_enabled="true"/>
            <file path="generate/src/OsIf_Cfg.c" update_enabled="true"/>
         </generated_project_files>
         <peripherals_profile>
            <processor_version>0.0.0</processor_version>
            <ignored_component_migration_offer>
               <component>BaseNXP</component>
            </ignored_component_migration_offer>
         </peripherals_profile>
         <functional_groups>
            <functional_group name="VS_0" uuid="343d8c09-29ab-44df-a8a2-0e35bf16a60f" called_from_default_init="true" id_prefix="" core="M7_0">
               <description></description>
               <options/>
               <dependencies/>
               <instances>
                  <instance name="BaseNXP" uuid="a62e3129-c7ba-41d9-97d3-6cb575e7e56b" type="BaseNXP" type_id="Base" mode="general" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="BaseNXP">
                        <setting name="Name" value="BaseNXP"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="OsIfGeneral">
                           <setting name="Name" value="OsIfGeneral"/>
                           <setting name="OsIfMulticoreSupport" value="false"/>
                           <setting name="OsIfEnableUserModeSupport" value="false"/>
                           <setting name="OsIfDevErrorDetect" value="true"/>
                           <setting name="OsIfUseSystemTimer" value="false"/>
                           <setting name="OsIfUseCustomTimer" value="false"/>
                           <setting name="OsIfUseGetUserId" value="GET_CORE_ID"/>
                           <setting name="OsIfInstanceId" value="255"/>
                           <struct name="OsIfOperatingSystemType">
                              <setting name="Name" value="OsIfOperatingSystemType"/>
                              <setting name="Choice" value="OsIfBaremetalType"/>
                              <struct name="OsIfBaremetalType" quick_selection="Default">
                                 <setting name="Name" value="OsIfBaremetalType"/>
                              </struct>
                           </struct>
                           <array name="OsIfEcucPartitionRef"/>
                           <array name="OsIfCounterConfig"/>
                        </struct>
                        <struct name="CommonPublishedInformation" quick_selection="Default">
                           <setting name="Name" value="CommonPublishedInformation"/>
                           <setting name="ModuleId" value="0"/>
                           <setting name="VendorId" value="43"/>
                           <array name="VendorApiInfix"/>
                           <setting name="ArReleaseMajorVersion" value="4"/>
                           <setting name="ArReleaseMinorVersion" value="4"/>
                           <setting name="ArReleaseRevisionVersion" value="0"/>
                           <setting name="SwMajorVersion" value="4"/>
                           <setting name="SwMinorVersion" value="0"/>
                           <setting name="SwPatchVersion" value="2"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="Adc_Sar_Ip" uuid="dca0cef7-6609-43e1-be55-7c9bcdace427" type="Adc_Sar_Ip" type_id="Adc_Sar_Ip" mode="ip" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Adc_Sar_Ip">
                        <setting name="Name" value="Adc_Sar_Ip"/>
                        <struct name="AdcSarGeneral">
                           <setting name="Name" value="AdcSarGeneral"/>
                           <setting name="AdcSarIpDevErrorDetect" value="false"/>
                           <setting name="AdcSarTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="AdcSarTimeoutVal" value="100000"/>
                           <setting name="AdcSarEnableUserModeSupport" value="false"/>
                           <setting name="AdcSarEnableWatchdogApi" value="true"/>
                           <setting name="AdcSarEnableSelfTestApi" value="false"/>
                           <setting name="AdcSarEnableEoC" value="true"/>
                           <setting name="AdcSarEnableAsyncCalibration" value="false"/>
                        </struct>
                        <array name="AdcHwUnit">
                           <struct name="0">
                              <setting name="Name" value="AdcHwUnit_0"/>
                              <setting name="AdcHwUnitId" value="ADC0"/>
                              <setting name="AdcHwUnitConvMode" value="ADC_SAR_IP_CONV_MODE_ONESHOT"/>
                              <setting name="AdcPrescale" value="1"/>
                              <setting name="AdcCalibrationPrescale" value="1"/>
                              <setting name="AdcPowerDownDelay" value="0"/>
                              <setting name="AdcAutoClockOff" value="false"/>
                              <setting name="AdcBypassSampling" value="false"/>
                              <setting name="AdcHwUnitOverwriteEn" value="false"/>
                              <setting name="AdcPresamplingInternalSignal0" value="VREFL"/>
                              <setting name="AdcPresamplingInternalSignal1" value="VREFL"/>
                              <setting name="AdcHwUnitCtuMode" value="ADC_SAR_IP_CTU_MODE_TRIGGER"/>
                              <setting name="AdcHwUnitExtInjTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                              <setting name="AdcHwUnitExtNrmTrg" value="ADC_SAR_IP_EXT_TRIG_EDGE_DISABLED"/>
                              <setting name="AdcHwUnitPrimaryExtNrmTrg" value="false"/>
                              <setting name="AdcHwUnitUsrOffset" value="0"/>
                              <setting name="AdcHwUnitUsrGain" value="0"/>
                              <setting name="AdcHwUnitDmaEnable" value="false"/>
                              <setting name="AdcHwUnitDmaClearSource" value="DMA_REQ_CLEAR_ON_ACK"/>
                              <setting name="AdcHwUnitNotifChainN" value="AdcEndOfChainNotif"/>
                              <setting name="AdcHwUnitNotifChainJ" value="AdcEndOfChainNotif"/>
                              <setting name="AdcHwUnitNotifCtu" value="NULL_PTR"/>
                              <setting name="AdcHwUnitNotifEOC" value="NULL_PTR"/>
                              <setting name="AdcHwUnitNotifWdg" value="NULL_PTR"/>
                              <setting name="AdcHwUnitDataAlign" value="ADC_SAR_IP_DATA_ALIGNED_RIGHT"/>
                              <array name="AdcSelfTestThresholdConfiguration"/>
                              <setting name="AdcSamplingDurationNormal0" value="255"/>
                              <setting name="AdcSamplingDurationNormal1" value="255"/>
                              <array name="AdcChannel">
                                 <struct name="0">
                                    <setting name="Name" value="AdcChannel_0"/>
                                    <setting name="AdcChannelName" value="BANDGAP_ChanNum32"/>
                                    <setting name="NormalChnEn" value="true"/>
                                    <setting name="InjectedChnEn" value="true"/>
                                    <setting name="AdcEnablePresampling" value="false"/>
                                    <setting name="AdcEnableThresholds" value="false"/>
                                    <array name="AdcThresholdRegister"/>
                                    <setting name="WdgEn" value="false"/>
                                    <setting name="EOCEn" value="true"/>
                                    <setting name="DmaEn" value="false"/>
                                 </struct>
                              </array>
                              <array name="AdcThresholdControl"/>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
                  <instance name="Ctu_Ip" uuid="62f9a131-3d1e-412d-94d9-e39f9de2a31a" type="Ctu_Ip" type_id="Ctu_Ip" mode="ip" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="Ctu_Ip">
                        <setting name="Name" value="Ctu_Ip"/>
                        <struct name="CtuGeneral">
                           <setting name="Name" value="CtuGeneral"/>
                           <setting name="CtuIpDevErrorDetect" value="false"/>
                           <setting name="CtuTimeoutMethod" value="OSIF_COUNTER_DUMMY"/>
                           <setting name="CtuTimeoutValue" value="3000"/>
                           <setting name="CtuEnableUserModeSupport" value="false"/>
                        </struct>
                        <array name="CtuHwUnit">
                           <struct name="0">
                              <setting name="Name" value="CtuHwUnit_0"/>
                              <setting name="CtuHwUnitId" value="0"/>
                              <setting name="CtuTGSMode" value="TGS_MODE_TRIGGERED"/>
                              <setting name="CtuInputClockPrescaler" value="PRESCALER_1"/>
                              <setting name="CtuExtTrigMode" value="EXT_TRIG_MODE_PULSE"/>
                              <setting name="CtuTgsCounterCompareVal" value="0xFFFF"/>
                              <setting name="CtuTgsCounterReloadVal" value="0x0001"/>
                              <setting name="CtuAdcCmdListMode" value="ADC_CMD_LIST_MODE_STREAMING"/>
                              <setting name="CtuSeqModeMrsInput" value="PWM_REL_FTM0_CH0"/>
                              <setting name="CtuSeqModeMrsInputEdge" value="EDGE_RISING"/>
                              <setting name="CtuDmaDoneGRE" value="false"/>
                              <setting name="CtuDmaReqMRS" value="false"/>
                              <setting name="CtuFifoDmaRawData" value="false"/>
                              <setting name="CtuDisableOutput" value="false"/>
                              <setting name="CtuErrorNotif" value="NULL_PTR"/>
                              <setting name="CtuMrsNotif" value="NULL_PTR"/>
                              <setting name="CtuAdcCmdIssueNotif" value="NULL_PTR"/>
                              <setting name="CtuDigitalFilter" value="0"/>
                              <setting name="CtuControlOnTime" value="0"/>
                              <setting name="CtuExpectedValuePortA" value="0xFFFF"/>
                              <setting name="CtuExpectedNotifPortA" value="NULL_PTR"/>
                              <setting name="CtuExpectedValuePortB" value="0xFFFF"/>
                              <setting name="CtuExpectedNotifPortB" value="NULL_PTR"/>
                              <setting name="CtuConvDurationCounterRange" value="0xFFFF"/>
                              <array name="CtuInputTrigConfigs">
                                 <struct name="0">
                                    <setting name="Name" value="CtuInputTrigConfigs_0"/>
                                    <setting name="CtuInputTrigSelect" value="PWM_REL_FTM0_CH0"/>
                                    <setting name="CtuInputTrigEdge" value="EDGE_RISING"/>
                                 </struct>
                              </array>
                              <array name="CtuTriggerCfg">
                                 <struct name="0">
                                    <setting name="Name" value="CtuTriggerCfg_0"/>
                                    <setting name="CtuTriggerIndex" value="0"/>
                                    <setting name="CtuCompareVal" value="15"/>
                                    <setting name="CtuCmdListStartAdr" value="0"/>
                                    <array name="CtuOutputTrigArray">
                                       <struct name="0">
                                          <setting name="Name" value="OutputTrig_0"/>
                                          <setting name="CtuOutputTrigSel" value="E"/>
                                          <setting name="CtuOutputTrigEn" value="true"/>
                                       </struct>
                                       <struct name="1">
                                          <setting name="Name" value="OutputTrig_1"/>
                                          <setting name="CtuOutputTrigSel" value="ADCE"/>
                                          <setting name="CtuOutputTrigEn" value="true"/>
                                       </struct>
                                    </array>
                                    <setting name="CtuTriggerNotif" value="NULL_PTR"/>
                                 </struct>
                              </array>
                              <array name="CtuAdcCommandList">
                                 <struct name="0">
                                    <setting name="Name" value="CtuAdcCommandList_0"/>
                                    <setting name="CtuIntEn" value="false"/>
                                    <setting name="CtuFifoIdx" value="0"/>
                                    <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                    <setting name="CtuLastCmd" value="NOT_LAST"/>
                                    <setting name="CtuAdcPort" value="ADC_0"/>
                                    <setting name="CtuAdcChanA" value="ADC_CH_00_ChanNum0"/>
                                    <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                 </struct>
                                 <struct name="1">
                                    <setting name="Name" value="CtuAdcCommandList_1"/>
                                    <setting name="CtuIntEn" value="false"/>
                                    <setting name="CtuFifoIdx" value="0"/>
                                    <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                    <setting name="CtuLastCmd" value="NOT_LAST"/>
                                    <setting name="CtuAdcPort" value="ADC_0"/>
                                    <setting name="CtuAdcChanA" value="ADC_CH_01_ChanNum1"/>
                                    <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                 </struct>
                                 <struct name="2">
                                    <setting name="Name" value="CtuAdcCommandList_2"/>
                                    <setting name="CtuIntEn" value="false"/>
                                    <setting name="CtuFifoIdx" value="0"/>
                                    <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                    <setting name="CtuLastCmd" value="NOT_LAST"/>
                                    <setting name="CtuAdcPort" value="ADC_0"/>
                                    <setting name="CtuAdcChanA" value="ADC_CH_02_ChanNum2"/>
                                    <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                 </struct>
                                 <struct name="3">
                                    <setting name="Name" value="CtuAdcCommandList_3"/>
                                    <setting name="CtuIntEn" value="false"/>
                                    <setting name="CtuFifoIdx" value="0"/>
                                    <setting name="CtuConvMode" value="CONV_MODE_SINGLE"/>
                                    <setting name="CtuLastCmd" value="LAST"/>
                                    <setting name="CtuAdcPort" value="ADC_0"/>
                                    <setting name="CtuAdcChanA" value="ADC_CH_03_ChanNum3"/>
                                    <setting name="CtuAdcChanB" value="ADC_CH_08_ChanNum0"/>
                                 </struct>
                              </array>
                              <array name="CtuResultFifos">
                                 <struct name="0">
                                    <setting name="Name" value="CtuResultFifos_0"/>
                                    <setting name="CtuFifoIndex" value="0"/>
                                    <setting name="CtuFifoThreshold" value="2"/>
                                    <setting name="CtuFifoDmaEn" value="false"/>
                                    <setting name="CtuFifoDmaBuffer" value="CtuDmaFifo0"/>
                                    <array name="CtuFifoDmaChannelId"/>
                                    <setting name="CtuFifoThresholdNotif" value="CtuThresholdNotif"/>
                                    <setting name="CtuFifoUnderrunNotif" value="NULL_PTR"/>
                                    <setting name="CtuFifoOverrunNotif" value="NULL_PTR"/>
                                    <setting name="CtuFifoFullNotif" value="NULL_PTR"/>
                                 </struct>
                              </array>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
                  <instance name="IntCtrl_Ip" uuid="6de19f54-d666-40bf-9fae-7f5c5e9a6875" type="IntCtrl_Ip" type_id="IntCtrl_Ip" mode="ip" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="IntCtrl_Ip" quick_selection="PlatformDefault">
                        <setting name="Name" value="IntCtrl_Ip"/>
                        <struct name="ConfigTimeSupport">
                           <setting name="POST_BUILD_VARIANT_USED" value="false"/>
                           <setting name="IMPLEMENTATION_CONFIG_VARIANT" value="VARIANT-PRE-COMPILE"/>
                        </struct>
                        <struct name="IntCtrlConfigGeneral">
                           <setting name="Name" value="IntCtrlConfigGeneral"/>
                           <setting name="IntCtrlDevErrorDetect" value="true"/>
                           <setting name="PlatformEnableUserModeSupport" value="false"/>
                        </struct>
                        <array name="IntCtrlConfig"/>
                     </config_set>
                  </instance>
               </instances>
            </functional_group>
         </functional_groups>
         <components>
            <component name="system" uuid="03e26a3f-7208-4cd4-b7d4-9c307e9fd132" type_id="system">
               <config_set_global name="SystemModel">
                  <setting name="Name" value="SystemModel"/>
                  <setting name="EcvdGenerationMethod" value="INDIVIDUAL"/>
                  <setting name="EcvdOutputPath" value=""/>
                  <setting name="EcvdGenerationTrigger" value="Generate Configuration"/>
                  <setting name="SyncFunctionalGroups" value="true"/>
                  <setting name="IgnoreComponentSuffix" value="true"/>
                  <setting name="ComponentGenerationMethod" value="FunctionalGroups"/>
                  <setting name="DefaultFunctionalGroup" value="VS_0"/>
                  <struct name="PostBuildSelectable" quick_selection="Default">
                     <setting name="Name" value="PostBuildSelectable"/>
                     <array name="PredefinedVariants">
                        <struct name="0">
                           <setting name="Name" value="BOARD_InitPeripherals"/>
                           <setting name="Path" value="/system/SystemModel/PostBuildSelectable/BOARD_InitPeripherals"/>
                           <array name="PostBuildVariantCriterionValues"/>
                        </struct>
                     </array>
                  </struct>
                  <struct name="Criterions" quick_selection="Default">
                     <setting name="Name" value="Criterions"/>
                     <array name="PostBuildVariantCriterions"/>
                  </struct>
               </config_set_global>
            </component>
         </components>
      </periphs>
   </tools>
</configuration>