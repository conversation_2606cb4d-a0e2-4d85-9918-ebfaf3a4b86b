<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Adc" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Adc" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Adc"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile"/>
              <d:ctr name="AdcConfigSet" type="IDENTIFIABLE">
                <d:lst name="AdcHwUnit" type="MAP">
                  <d:ctr name="AdcHwUnit_0" type="IDENTIFIABLE">
                    <d:var name="AdcTransferType" type="ENUMERATION" 
                           value="ADC_INTERRUPT"/>
                    <d:ref name="AdcDmaChannelId" type="REFERENCE" value="">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:ref name="AdcCountingDmaChannelId" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="AdcClockSource" type="ENUMERATION" 
                           value="BUS_CLOCK">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitId" type="ENUMERATION" value="ADC0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="AdcLogicalUnitId" type="INTEGER" value="1"/>
                    <d:var name="AdcPrescale" type="INTEGER" value="1">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="AdcAltPrescale" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcCalibrationPrescale" type="INTEGER" 
                           value="2">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPowerDownDelay" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcAltPowerDownDelay" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcAutoClockOff" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcBypassSampling" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitOverwriteEn" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPresamplingInternalSignal0" 
                           type="ENUMERATION" value="VREFH"/>
                    <d:var name="AdcPresamplingInternalSignal1" 
                           type="ENUMERATION" value="AVDD"/>
                    <d:var name="AdcPresamplingInternalSignal2" 
                           type="ENUMERATION" value="DVDD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitUsrOffset" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitUsrGain" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitDmaClearSource" type="ENUMERATION" 
                           value="DMA_REQ_CLEAR_ON_ACK">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="AdcSelfTestThresholdConfiguration" 
                           type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="AdcSTAW0RSelfTestHighThresholdValue" 
                             type="INTEGER" value="2912">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW0RSelfTestLowThresholdValue" 
                             type="INTEGER" value="2367">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1ARSelfTestHighThresholdValue" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1ARSelfTestLowThresholdValue" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1BRSelfTestHighThresholdValue" 
                             type="INTEGER" value="2867">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1BRSelfTestLowThresholdValue" 
                             type="INTEGER" value="1414">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW2RSelfTestLowThresholdValue" 
                             type="INTEGER" value="4085">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW4RSelfTestHighThresholdValue" 
                             type="INTEGER" value="100">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW5RSelfTestHighThresholdValue" 
                             type="INTEGER" value="100">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="AdcNormalConvTimings" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="AdcSamplingDurationNormal0" type="INTEGER" 
                             value="22">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="AdcSamplingDurationNormal1" type="INTEGER" 
                             value="22">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="AdcAlternateConvTimings" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="AdcSamplingDurationAlt0" type="INTEGER" 
                             value="22">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="AdcSamplingDurationAlt1" type="INTEGER" 
                             value="22">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:lst name="AdcChannel" type="MAP">
                      <d:ctr name="Adc0Channel_1" type="IDENTIFIABLE">
                        <d:var name="AdcChannelId" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="ADC_CH_01_ChanNum1"/>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="true"/>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/AdcThresholdControl_0">
                          <a:a name="ENABLE" value="true"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="Notification_WDG_0"/>
                      </d:ctr>
                      <d:ctr name="Adc0Channel_5" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="1"/>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="ADC_CH_05_ChanNum5">
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelId" type="INTEGER" value="5">
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="true">
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/AdcThresholdControl_1">
                          <a:a name="ENABLE" value="true"/>
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="Notification_WDG_1">
                          <a:a name="VARIANTS" type="Variant">
                            <variant:pbvcond>
                              <variant:criterion 
                                                 value="ASPath:/EB/PostBuildSelectable/PostBuildSelectableCriterion"/>
                              <variant:cond>
                                <variant:tst expr="0"/>
                              </variant:cond>
                            </variant:pbvcond>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc0_VREFL" type="IDENTIFIABLE">
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="VREFL_ChanNum37"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="37"/>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc0_VREFH" type="IDENTIFIABLE">
                        <d:var name="AdcChannelId" type="INTEGER" value="38">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="VREFH_ChanNum38"/>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc0_BANDGAP" type="IDENTIFIABLE">
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="BANDGAP_ChanNum32"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="32"/>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc0_AVDD" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="5">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="AVDD_ChanNum36"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="36">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc0_DVDD" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="DVDD_DIV2_ChanNum35"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="35">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="AdcGroup" type="MAP">
                      <d:ctr name="AdcGroup_0" type="IDENTIFIABLE">
                        <d:var name="AdcGroupAccessMode" type="ENUMERATION" 
                               value="ADC_ACCESS_MODE_SINGLE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupConversionMode" type="ENUMERATION" 
                               value="ADC_CONV_MODE_ONESHOT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupConversionType" type="ENUMERATION" 
                               value="ADC_CONV_TYPE_NORMAL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcGroupPriority" type="INTEGER" value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupReplacement" type="ENUMERATION" 
                               value="ADC_GROUP_REPL_ABORT_RESTART">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupTriggSrc" type="ENUMERATION" 
                               value="ADC_TRIGG_SRC_SW"/>
                        <d:ref name="AdcGroupHwTriggerSource" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwTrigger_1">
                          <a:a name="ENABLE" value="true"/>
                        </d:ref>
                        <d:var name="AdcHwTrigSignal" type="ENUMERATION" 
                               value="ADC_HW_TRIG_RISING_EDGE">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcHwTrigTimer" type="INTEGER" value="0">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                        <d:var name="AdcNotification" type="FUNCTION-NAME" 
                               value="Notification_0">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                        <d:var name="AdcExtraNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcDmaErrorNotification" 
                               type="FUNCTION-NAME" value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamingBufferMode" type="ENUMERATION" 
                               value="ADC_STREAM_BUFFER_LINEAR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableOptimizeDmaStreamingGroups" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableHalfInterrupt" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamingNumSamples" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamResultGroup" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableChDisableChGroup" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcWithoutInterrupts" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcWithoutDma" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcExtDMAChanEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="AdcGroupDefinition">
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_VREFL"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_VREFH"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_BANDGAP"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_AVDD"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnit_0/Adc0_DVDD"/>
                        </d:lst>
                        <d:lst name="AdcGroupEcucPartitionRef"/>
                        <d:ctr name="AdcGroupConversionConfiguration" 
                               type="IDENTIFIABLE">
                          <d:var name="AdcSamplingDuration0" type="INTEGER" 
                                 value="255"/>
                          <d:var name="AdcSamplingDuration1" type="INTEGER" 
                                 value="255"/>
                        </d:ctr>
                        <d:ctr name="AdcAlternateGroupConvTimings" 
                               type="IDENTIFIABLE">
                          <d:var name="AdcAltGroupSamplingDuration0" 
                                 type="INTEGER" value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="AdcAltGroupSamplingDuration1" 
                                 type="INTEGER" value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="AdcThresholdControl" type="MAP">
                      <d:ctr name="AdcThresholdControl_0" type="IDENTIFIABLE">
                        <d:var name="AdcThresholdControlRegister" 
                               type="ENUMERATION" value="ADC_THRESHOLD_REG_1"/>
                        <d:var name="AdcHighThreshold" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                        <d:var name="AdcLowThreshold" type="INTEGER" value="0">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="AdcThresholdControl_1" type="IDENTIFIABLE">
                        <d:var name="AdcThresholdControlRegister" 
                               type="ENUMERATION" value="ADC_THRESHOLD_REG_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcHighThreshold" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                        <d:var name="AdcLowThreshold" type="INTEGER" value="0">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="AdcHwUnitEcucPartitionRef"/>
                  </d:ctr>
                  <d:ctr name="AdcHwUnitDma" type="IDENTIFIABLE">
                    <d:var name="AdcHwUnitId" type="ENUMERATION" value="ADC1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="AdcLogicalUnitId" type="INTEGER" value="0"/>
                    <d:var name="AdcTransferType" type="ENUMERATION" 
                           value="ADC_DMA"/>
                    <d:ref name="AdcDmaChannelId" type="REFERENCE" 
                           value="ASPath:/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1">
                      <a:a name="ENABLE" value="true"/>
                    </d:ref>
                    <d:ref name="AdcCountingDmaChannelId" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="AdcClockSource" type="ENUMERATION" 
                           value="BUS_CLOCK">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPrescale" type="INTEGER" value="1">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcAltPrescale" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcCalibrationPrescale" type="INTEGER" 
                           value="2">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPowerDownDelay" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcAltPowerDownDelay" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcAutoClockOff" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcBypassSampling" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitOverwriteEn" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPresamplingInternalSignal0" 
                           type="ENUMERATION" value="DVDD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPresamplingInternalSignal1" 
                           type="ENUMERATION" value="DVDD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcPresamplingInternalSignal2" 
                           type="ENUMERATION" value="DVDD">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitUsrOffset" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitUsrGain" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AdcHwUnitDmaClearSource" type="ENUMERATION" 
                           value="DMA_REQ_CLEAR_ON_ACK">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="AdcSelfTestThresholdConfiguration" 
                           type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="AdcSTAW0RSelfTestHighThresholdValue" 
                             type="INTEGER" value="2912">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW0RSelfTestLowThresholdValue" 
                             type="INTEGER" value="2367">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1ARSelfTestHighThresholdValue" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1ARSelfTestLowThresholdValue" 
                             type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1BRSelfTestHighThresholdValue" 
                             type="INTEGER" value="2867">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW1BRSelfTestLowThresholdValue" 
                             type="INTEGER" value="1414">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW2RSelfTestLowThresholdValue" 
                             type="INTEGER" value="4085">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW4RSelfTestHighThresholdValue" 
                             type="INTEGER" value="100">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="AdcSTAW5RSelfTestHighThresholdValue" 
                             type="INTEGER" value="100">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="AdcNormalConvTimings" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="AdcSamplingDurationNormal0" type="INTEGER" 
                             value="20">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="AdcSamplingDurationNormal1" type="INTEGER" 
                             value="20">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="AdcAlternateConvTimings" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="AdcSamplingDurationAlt0" type="INTEGER" 
                             value="20">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="AdcSamplingDurationAlt1" type="INTEGER" 
                             value="20">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:lst name="AdcChannel" type="MAP">
                      <d:ctr name="Adc1Channel_1" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelId" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1Channel_5" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="ADC_CH_09_ChanNum1"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1_VREFH" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="VREFH_ChanNum38"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="38">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1_VREFL" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="VREFL_ChanNum37"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="37">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1_BANDGAP" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="BANDGAP_ChanNum32"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="32">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1_DVDD" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="5">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="DVDD_DIV2_ChanNum35"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="35">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="Adc1_AVDD" type="IDENTIFIABLE">
                        <d:var name="AdcLogicalChannelId" type="INTEGER" 
                               value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelName" type="ENUMERATION" 
                               value="AVDD_ChanNum36"/>
                        <d:var name="AdcChannelId" type="INTEGER" value="36">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="AdcChannelConvTime" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLimitCheck" type="BOOLEAN" 
                               value="false">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelHighLimit" type="INTEGER" 
                               value="4095">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelLowLimit" type="INTEGER" 
                               value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRangeSelect" type="ENUMERATION" 
                               value="ADC_RANGE_ALWAYS">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcHigh" 
                               type="ENUMERATION" value="UPPER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelRefVoltsrcLow" 
                               type="ENUMERATION" value="LOWER_REF_VOLT_0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelResolution" type="INTEGER" 
                               value="12">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcChannelSampTime" type="INTEGER" 
                               value="8">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnablePresampling" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableThresholds" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="AdcThresholdRegister" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="AdcWdogNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="AdcGroup" type="MAP">
                      <d:ctr name="AdcSwGroupWithDma" type="IDENTIFIABLE">
                        <d:var name="AdcGroupAccessMode" type="ENUMERATION" 
                               value="ADC_ACCESS_MODE_SINGLE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupConversionMode" type="ENUMERATION" 
                               value="ADC_CONV_MODE_ONESHOT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupConversionType" type="ENUMERATION" 
                               value="ADC_CONV_TYPE_NORMAL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupId" type="INTEGER" value="1"/>
                        <d:var name="AdcGroupPriority" type="INTEGER" value="0">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupReplacement" type="ENUMERATION" 
                               value="ADC_GROUP_REPL_ABORT_RESTART">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcGroupTriggSrc" type="ENUMERATION" 
                               value="ADC_TRIGG_SRC_SW"/>
                        <d:ref name="AdcGroupHwTriggerSource" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwTrigger_1">
                          <a:a name="ENABLE" value="true"/>
                        </d:ref>
                        <d:var name="AdcHwTrigSignal" type="ENUMERATION" 
                               value="ADC_HW_TRIG_RISING_EDGE">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcHwTrigTimer" type="INTEGER" value="10">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcNotification" type="FUNCTION-NAME" 
                               value="Notification_1">
                          <a:a name="ENABLE" value="true"/>
                        </d:var>
                        <d:var name="AdcExtraNotification" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcDmaErrorNotification" 
                               type="FUNCTION-NAME" value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamingBufferMode" type="ENUMERATION" 
                               value="ADC_STREAM_BUFFER_LINEAR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableOptimizeDmaStreamingGroups" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableHalfInterrupt" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamingNumSamples" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcStreamResultGroup" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcEnableChDisableChGroup" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcWithoutInterrupts" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcWithoutDma" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="AdcExtDMAChanEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:lst name="AdcGroupDefinition">
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_VREFL"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_VREFH"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_BANDGAP"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_AVDD"/>
                          <d:ref type="REFERENCE" 
                                 value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwUnitDma/Adc1_DVDD"/>
                        </d:lst>
                        <d:lst name="AdcGroupEcucPartitionRef"/>
                        <d:ctr name="AdcGroupConversionConfiguration" 
                               type="IDENTIFIABLE">
                          <d:var name="AdcSamplingDuration0" type="INTEGER" 
                                 value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="AdcSamplingDuration1" type="INTEGER" 
                                 value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="AdcAlternateGroupConvTimings" 
                               type="IDENTIFIABLE">
                          <d:var name="AdcAltGroupSamplingDuration0" 
                                 type="INTEGER" value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="AdcAltGroupSamplingDuration1" 
                                 type="INTEGER" value="22">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="AdcThresholdControl" type="MAP"/>
                    <d:lst name="AdcHwUnitEcucPartitionRef"/>
                  </d:ctr>
                </d:lst>
                <d:lst name="AdcHwTrigger" type="MAP">
                  <d:ctr name="AdcHwTrigger_1" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_ODD_3_FTM1_CH1"/>
                  </d:ctr>
                  <d:ctr name="AdcHwTrigger_2" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_ODD_1_FTM0_CH2"/>
                  </d:ctr>
                  <d:ctr name="AdcHwTrigger_4" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_REL_FTM0_CH0"/>
                  </d:ctr>
                  <d:ctr name="AdcHwTrigger_5" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_ODD_0_FTM0_CH1"/>
                  </d:ctr>
                  <d:ctr name="AdcHwTrigger_6" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_ODD_2_FTM1_CH0"/>
                  </d:ctr>
                  <d:ctr name="AdcHwTrigger_7" type="IDENTIFIABLE">
                    <d:var name="AdcHwTrigSrc" type="ENUMERATION" 
                           value="CTU_PWM_EVEN_0_FTM1_CH2"/>
                  </d:ctr>
                </d:lst>
                <d:lst name="CtuHwUnit" type="MAP">
                  <d:ctr name="CtuHwUnit_0" type="IDENTIFIABLE">
                    <d:var name="CtuHwUnitId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="CtuLogicalUnitId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="CtuTGSMode" type="ENUMERATION" 
                           value="TGS_MODE_SEQUENTIAL"/>
                    <d:var name="CtuInputClockPrescaler" type="ENUMERATION" 
                           value="PRESCALER_1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuExtTrigMode" type="ENUMERATION" 
                           value="EXT_TRIG_MODE_PULSE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuTgsCounterCompareVal" type="INTEGER" 
                           value="256"/>
                    <d:var name="CtuTgsCounterReloadVal" type="INTEGER" 
                           value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuAdcCmdListMode" type="ENUMERATION" 
                           value="ADC_CMD_LIST_MODE_STREAMING">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuSeqModeMrsInput" type="ENUMERATION" 
                           value="PWM_REL_FTM0_CH0"/>
                    <d:var name="CtuSeqModeMrsInputEdge" type="ENUMERATION" 
                           value="EDGE_BOTH">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuDmaDoneGRE" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuDmaReqMRS" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuFifoDmaRawData" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuDisableOutput" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuDigitalFilter" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuControlOnTime" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="ExpectedConvDurationConfig" 
                           type="IDENTIFIABLE">
                      <d:var name="CtuExpectedValuePortA" type="INTEGER" 
                             value="65535">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CtuExpectedNotifPortA" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CtuExpectedValuePortB" type="INTEGER" 
                             value="65535">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CtuExpectedNotifPortB" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CtuConvDurationCounterRange" type="INTEGER" 
                             value="65535">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:var name="CtuErrorNotif" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuMrsNotif" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CtuAdcCmdIssueNotif" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="CtuInputTrigConfigs" type="MAP">
                      <d:ctr name="CtuInputTrigConfigs_0" type="IDENTIFIABLE">
                        <d:ref name="CtuInputTrigSelect" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwTrigger_4"/>
                        <d:var name="CtuInputTrigEdge" type="ENUMERATION" 
                               value="EDGE_RISING"/>
                      </d:ctr>
                      <d:ctr name="CtuInputTrigConfigs_1" type="IDENTIFIABLE">
                        <d:ref name="CtuInputTrigSelect" type="REFERENCE" 
                               value="ASPath:/Adc/Adc/AdcConfigSet/AdcHwTrigger_6"/>
                        <d:var name="CtuInputTrigEdge" type="ENUMERATION" 
                               value="EDGE_FALLING"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="CtuTriggerCfg" type="MAP">
                      <d:ctr name="CtuTriggerCfg_0" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="10"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="65"/>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_1" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="100"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="1"/>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_2" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="200"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="0"/>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_3" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="300"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="64"/>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_4" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="400"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="1"/>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_5" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="5">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="500"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_6" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="600"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuTriggerCfg_7" type="IDENTIFIABLE">
                        <d:var name="CtuTriggerIndex" type="INTEGER" value="7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuCompareVal" type="INTEGER" value="700"/>
                        <d:var name="CtuCmdListStartAdr" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuOutputTrigEnMask" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuTriggerNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="CtuAdcCommandList" type="MAP">
                      <d:ctr name="CtuAdcCommandList_0" type="IDENTIFIABLE">
                        <d:var name="CtuIntEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoIdx" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuConvMode" type="ENUMERATION" 
                               value="CONV_MODE_SINGLE"/>
                        <d:var name="CtuLastCmd" type="ENUMERATION" 
                               value="NOT_LAST"/>
                        <d:var name="CtuAdcPort" type="ENUMERATION" 
                               value="ADC_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuAdcChanA" type="ENUMERATION" 
                               value="ADC_CH_01_ChanNum1"/>
                        <d:var name="CtuAdcChanB" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0"/>
                      </d:ctr>
                      <d:ctr name="CtuAdcCommandList_1" type="IDENTIFIABLE">
                        <d:var name="CtuIntEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoIdx" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuConvMode" type="ENUMERATION" 
                               value="CONV_MODE_SINGLE"/>
                        <d:var name="CtuLastCmd" type="ENUMERATION" 
                               value="NOT_LAST"/>
                        <d:var name="CtuAdcPort" type="ENUMERATION" 
                               value="ADC_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuAdcChanA" type="ENUMERATION" 
                               value="ADC_CH_01_ChanNum1"/>
                        <d:var name="CtuAdcChanB" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0"/>
                      </d:ctr>
                      <d:ctr name="CtuAdcCommandList_2" type="IDENTIFIABLE">
                        <d:var name="CtuIntEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoIdx" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuConvMode" type="ENUMERATION" 
                               value="CONV_MODE_SINGLE"/>
                        <d:var name="CtuLastCmd" type="ENUMERATION" 
                               value="NOT_LAST"/>
                        <d:var name="CtuAdcPort" type="ENUMERATION" 
                               value="ADC_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuAdcChanA" type="ENUMERATION" 
                               value="ADC_CH_01_ChanNum1"/>
                        <d:var name="CtuAdcChanB" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0"/>
                      </d:ctr>
                      <d:ctr name="CtuAdcCommandList_3" type="IDENTIFIABLE">
                        <d:var name="CtuIntEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoIdx" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuConvMode" type="ENUMERATION" 
                               value="CONV_MODE_SINGLE"/>
                        <d:var name="CtuLastCmd" type="ENUMERATION" 
                               value="NOT_LAST"/>
                        <d:var name="CtuAdcPort" type="ENUMERATION" 
                               value="ADC_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuAdcChanA" type="ENUMERATION" 
                               value="ADC_CH_05_ChanNum5"/>
                        <d:var name="CtuAdcChanB" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0"/>
                      </d:ctr>
                      <d:ctr name="CtuAdcCommandList_4" type="IDENTIFIABLE">
                        <d:var name="CtuIntEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoIdx" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuConvMode" type="ENUMERATION" 
                               value="CONV_MODE_SINGLE"/>
                        <d:var name="CtuLastCmd" type="ENUMERATION" value="LAST"/>
                        <d:var name="CtuAdcPort" type="ENUMERATION" 
                               value="ADC_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuAdcChanA" type="ENUMERATION" 
                               value="ADC_CH_02_ChanNum2"/>
                        <d:var name="CtuAdcChanB" type="ENUMERATION" 
                               value="ADC_CH_08_ChanNum0"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="CtuResultFifos" type="MAP">
                      <d:ctr name="CtuResultFifos_0" type="IDENTIFIABLE">
                        <d:var name="CtuFifoIndex" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuFifoThreshold" type="INTEGER" value="3"/>
                        <d:var name="CtuFifoDmaEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaBuffer" type="LINKER-SYMBOL" 
                               value="CtuDmaFifo0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:ref name="CtuFifoDmaChannelId" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="CtuFifoThresholdNotif" 
                               type="FUNCTION-NAME" 
                               value="Fifo0ThresholdNotification"/>
                        <d:var name="CtuFifoUnderrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoOverrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoFullNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuResultFifos_1" type="IDENTIFIABLE">
                        <d:var name="CtuFifoIndex" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuFifoThreshold" type="INTEGER" value="3"/>
                        <d:var name="CtuFifoDmaEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaBuffer" type="LINKER-SYMBOL" 
                               value="CtuDmaFifo1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:ref name="CtuFifoDmaChannelId" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="CtuFifoThresholdNotif" 
                               type="FUNCTION-NAME" 
                               value="Fifo1ThresholdNotification"/>
                        <d:var name="CtuFifoUnderrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoOverrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoFullNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuResultFifos_2" type="IDENTIFIABLE">
                        <d:var name="CtuFifoIndex" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuFifoThreshold" type="INTEGER" value="8">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaBuffer" type="LINKER-SYMBOL" 
                               value="CtuDmaFifo2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:ref name="CtuFifoDmaChannelId" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="CtuFifoThresholdNotif" 
                               type="FUNCTION-NAME" 
                               value="Fifo2ThresholdNotification"/>
                        <d:var name="CtuFifoUnderrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoOverrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoFullNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="CtuResultFifos_3" type="IDENTIFIABLE">
                        <d:var name="CtuFifoIndex" type="INTEGER" value="3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="CtuFifoThreshold" type="INTEGER" value="8">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaEn" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoDmaBuffer" type="LINKER-SYMBOL" 
                               value="CtuDmaFifo3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:ref name="CtuFifoDmaChannelId" type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                        <d:var name="CtuFifoThresholdNotif" 
                               type="FUNCTION-NAME" 
                               value="Fifo3ThresholdNotification"/>
                        <d:var name="CtuFifoUnderrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoOverrunNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CtuFifoFullNotif" type="FUNCTION-NAME" 
                               value="NULL_PTR">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="AdcGeneral" type="IDENTIFIABLE">
                <d:var name="AdcDeInitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableLimitCheck" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableQueuing" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcPriorityQueueMaxDepth" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableStartStopGroupApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcGrpNotifCapability" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcHwTriggerApi" type="BOOLEAN" value="true"/>
                <d:var name="AdcPriorityImplementation" type="ENUMERATION" 
                       value="ADC_PRIORITY_NONE"/>
                <d:var name="AdcReadGroupApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcResultAlignment" type="ENUMERATION" 
                       value="ADC_ALIGN_RIGHT">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="AdcEcucPartitionRef"/>
                <d:ref name="AdcKernelEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:var name="AdcLowPowerStatesSupport" type="BOOLEAN" 
                       value="true">
                  <a:a name="ENABLE" value="true"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcPowerStateAsynchTransitionMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="AdcPowerStateConfig" type="MAP"/>
              </d:ctr>
              <d:lst name="AdcHwConfiguration" type="MAP">
                <d:ctr name="AdcHwConfiguration_0" type="IDENTIFIABLE">
                  <d:var name="AdcHwConfiguredId" type="ENUMERATION" 
                         value="ADC0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="AdcNormalInterruptEnable" type="BOOLEAN" 
                         value="true"/>
                  <d:var name="AdcInjectedInterruptEnable" type="BOOLEAN" 
                         value="true"/>
                  <d:var name="CtuFifoOfInterruptEnable" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="WdgThresholdEnable" type="BOOLEAN" value="true"/>
                  <d:var name="DmaTransferEnable" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="AdcHwConfiguration_1" type="IDENTIFIABLE">
                  <d:var name="AdcHwConfiguredId" type="ENUMERATION" 
                         value="ADC1">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="AdcNormalInterruptEnable" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="AdcInjectedInterruptEnable" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CtuFifoOfInterruptEnable" type="BOOLEAN" 
                         value="false"/>
                  <d:var name="WdgThresholdEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="DmaTransferEnable" type="BOOLEAN" value="true"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="AdcPublishedInformation" type="IDENTIFIABLE">
                <d:var name="AdcChannelValueSigned" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcGroupFirstChannelFixed" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcMaxChannelResolution" type="INTEGER" value="12">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="123">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="AutosarExt" type="IDENTIFIABLE">
                <d:var name="AdcSarIpDevErrorDetect" type="BOOLEAN" 
                       value="false"/>
                <d:var name="AdcTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcTimeoutVal" type="INTEGER" value="65536">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CtuIpDevErrorDetect" type="BOOLEAN" value="false"/>
                <d:var name="AdcMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcMaxPartitions" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableDmaErrorNotification" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CtuEnableDmaTransferMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcSetHwUnitPowerModeApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableGroupDependentChannelNames" 
                       type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcBypassAbortChainCheck" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableChDisableChApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcGetInjectedConvStatusApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableCtuTrigAutosarExtApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcCtuHardwareTriggerOptimization" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableDualClockMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableCalibration" type="BOOLEAN" value="true"/>
                <d:var name="AdcEnableAsyncCalibration" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableSelfTest" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableThresholdConfigurationApi" type="BOOLEAN" 
                       value="false"/>
                <d:var name="AdcCtuControlModeExtraApis" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableCtuControlModeApi" type="BOOLEAN" 
                       value="true"/>
                <d:var name="AdcEnableDmaTransferMode" type="BOOLEAN" 
                       value="true"/>
                <d:var name="AdcEnableWatchdogApi" type="BOOLEAN" value="true"/>
                <d:var name="AdcUseSoftwareInjectedGroups" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcUseHardwareNormalGroups" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcConvTimeOnce" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcOptimizeOneShotHwTriggerConversions" 
                       type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcOptimizeDmaStreamingGroups" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcPreSamplingOnce" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableSetChannel" type="BOOLEAN" value="false"/>
                <d:var name="AdcEnableInitialNotification" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableReadRawDataApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="AdcEnableGroupStreamingResultReorder" 
                       type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
