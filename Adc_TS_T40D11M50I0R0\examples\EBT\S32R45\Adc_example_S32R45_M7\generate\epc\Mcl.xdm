<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Mcl" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Mcl" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Mcl"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile"/>
              <d:ctr name="MclGeneral" type="IDENTIFIABLE">
                <d:var name="MclEnableDevErrorDetect" type="BOOLEAN" 
                       value="true"/>
                <d:var name="Mcl_VersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="MclEnableUserModeSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="MclEnableMulticoreSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="MclEnableVirtualAddressMappingSupport" 
                       type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="MclDma" type="IDENTIFIABLE">
                  <d:var name="MclEnableDma" type="BOOLEAN" value="true"/>
                  <d:var name="MclEnableCrc" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="MclCache" type="IDENTIFIABLE">
                  <d:var name="MclEnableCache" type="BOOLEAN" value="true"/>
                </d:ctr>
                <d:ctr name="MclFtmCommon" type="IDENTIFIABLE">
                  <d:var name="Mcl_FtmCommonTimebase" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="MclConfig" type="IDENTIFIABLE">
                <d:lst name="MclVirtualMemorySection" type="MAP"/>
                <d:lst name="dmaLogicInstance_ConfigType" type="MAP">
                  <d:ctr name="dmaLogicInstance_ConfigType_0" 
                         type="IDENTIFIABLE">
                    <d:var name="dmaLogicInstance_IdName" type="STRING" 
                           value="DMA_LOGIC_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicInstance_hwId" type="ENUMERATION" 
                           value="DMA_IP_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicInstance_enDebug" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="dmaLogicInstance_enRoundRobin" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="dmaLogicInstance_enHaltAfterError" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="dmaLogicInstance_enChLinking" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="dmaLogicInstance_enGlMasterIdReplication" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="dmaLogicInstance_EcucPartitionRef" 
                           type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="dmaLogicInstance_DmaCrc" type="IDENTIFIABLE">
                      <d:var name="dmaLogicInstance_DmaCrcEnSwapBit" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="dmaLogicInstance_DmaCrcEnSwapByte" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="dmaLogicInstance_DmaCrcEnGlobal" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="dmaLogicChannel_Type" type="MAP">
                  <d:ctr name="CHANNEL_FOR_ADC1" type="IDENTIFIABLE">
                    <d:var name="dmaLogicChannel_LogicName" type="STRING" 
                           value="DMA_LOGIC_CH_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwInstId" type="ENUMERATION" 
                           value="DMA_IP_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwChId" type="ENUMERATION" 
                           value="DMA_IP_HW_CH_16"/>
                    <d:var name="dmaLogicChannel_InterruptCallback" 
                           type="FUNCTION-NAME" 
                           value="Adc_Ipw_Adc1DmaTransferCompleteNotification"/>
                    <d:var name="dmaLogicChannel_ErrorInterruptCallback" 
                           type="FUNCTION-NAME" value="NULL_PTR">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="dmaLogicChannel_EcucPartitionRef" 
                           type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:var name="dmaLogicChannel_EnableGlobalConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableTransferConfig" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="dmaLogicChannel_EnableScatterGather" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableCrcConfig" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="dmaLogicChannel_ConfigType" 
                           type="IDENTIFIABLE">
                      <d:ctr name="dmaLogicChannel_GlobalConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_GlobalControlType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalControl_enMasterIdReplication" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaGlobalControl_enBufferedWrites" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalRequestType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalRequest_enDmamuxTrigger" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaGlobalRequest_enDmamuxSource" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalRequest_Dmamux0HwRequest" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_REQ_MUX0_DISABLED">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaGlobalRequest_Dmamux1HwRequest" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_REQ_MUX1_SARADC1"/>
                          <d:var name="dmaGlobalRequest_Dmamux2HwRequest" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_REQ_MUX2_DISABLED">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaGlobalRequest_Dmamux3HwRequest" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_REQ_MUX3_DISABLED">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaGlobalRequest_enDmaRequest" 
                                 type="BOOLEAN" value="false"/>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalInterruptType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalInterrupt_enDmaErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalPriorityType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalPriority_GroupPriority" 
                                 type="ENUMERATION" value="DMA_IP_GROUP_PRIO0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaGlobalPriority_LevelPriority" 
                                 type="ENUMERATION" value="DMA_IP_LEVEL_PRIO0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaGlobalPriority_enPreemption" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaGlobalPriority_disPreempt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_TransferConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_TransferControlType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaLogicChannelConfig_bandwidthControl" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_BWC_ENGINE_NO_STALL">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                 type="STRING" value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                 type="STRING" value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_TransferSourceType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaLogicChannelConfig_SourceAddressType" 
                                 type="STRING" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaTransferConfig_TransferSizeType" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var name="dmaLogicChannelConfig_SourceModuloType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr 
                               name="dmaLogicChannelConfig_TransferDestinationType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaLogicChannelConfig_DestinationAddressType" 
                                 type="STRING" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaTransferConfig_TransferSizeType" 
                                 type="ENUMERATION" 
                                 value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_DestinationModuloType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr 
                               name="dmaLogicChannelConfig_TransferMinorLoopType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaLogicChannelConfig_enSourceOffset" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaLogicChannelConfig_enDestinationOffset" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaLogicChannelConfig_OffsetValueType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ref 
                                 name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                 type="REFERENCE" >
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:ref>
                          <d:var name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr 
                               name="dmaLogicChannelConfig_TransferMajorLoopType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ref 
                                 name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                 type="REFERENCE" >
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:ref>
                          <d:var 
                                 name="dmaLogicChannelConfig_MajorLoopCountType" 
                                 type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_ScatterGatherConfigType" 
                             type="IDENTIFIABLE">
                        <d:lst 
                               name="dmaLogicChannelConfig_ScatterGatherArrayType" 
                               type="MAP">
                          <d:ctr 
                                 name="dmaLogicChannelConfig_ScatterGatherArrayType_0" 
                                 type="IDENTIFIABLE">
                            <d:ctr 
                                   name="dmaLogicChannelConfig_ScatterGatherElementConfigType" 
                                   type="IDENTIFIABLE">
                              <d:var 
                                     name="dmaLogicChannelConfig_ScatterGatherElementNameType" 
                                     type="STRING" 
                                     value="DMA_LOGIC_CH_0_SGA_ELEMENT_0">
                                <a:a name="IMPORTER_INFO">
                                  <a:v>@DEF</a:v>
                                  <a:v>@CALC</a:v>
                                </a:a>
                              </d:var>
                              <d:var 
                                     name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" 
                                     type="BOOLEAN" value="false"/>
                              <d:ref 
                                     name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" 
                                     type="REFERENCE" 
                                     value="ASPath:/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_1">
                                <a:a name="ENABLE" value="true"/>
                              </d:ref>
                              <d:var 
                                     name="dmaLogicChannelConfig_enScatterGatherConfig" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferControlType" 
                                     type="IDENTIFIABLE">
                                <d:var name="dmaLogicChannelConfig_enStart" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_bandwidthControl" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_BWC_ENGINE_NO_STALL">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferSourceType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferDestinationType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMinorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enSourceOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDestinationOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_OffsetValueType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMajorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MajorLoopCountType" 
                                       type="INTEGER" value="1">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:ctr>
                          <d:ctr 
                                 name="dmaLogicChannelConfig_ScatterGatherArrayType_1" 
                                 type="IDENTIFIABLE">
                            <d:ctr 
                                   name="dmaLogicChannelConfig_ScatterGatherElementConfigType" 
                                   type="IDENTIFIABLE">
                              <d:var 
                                     name="dmaLogicChannelConfig_ScatterGatherElementNameType" 
                                     type="STRING" 
                                     value="DMA_LOGIC_CH_0_SGA_ELEMENT_1">
                                <a:a name="IMPORTER_INFO">
                                  <a:v>@DEF</a:v>
                                  <a:v>@CALC</a:v>
                                </a:a>
                              </d:var>
                              <d:var 
                                     name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" 
                                     type="BOOLEAN" value="false"/>
                              <d:ref 
                                     name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" 
                                     type="REFERENCE" 
                                     value="ASPath:/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_2">
                                <a:a name="ENABLE" value="true"/>
                              </d:ref>
                              <d:var 
                                     name="dmaLogicChannelConfig_enScatterGatherConfig" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferControlType" 
                                     type="IDENTIFIABLE">
                                <d:var name="dmaLogicChannelConfig_enStart" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_bandwidthControl" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_BWC_ENGINE_NO_STALL">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferSourceType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferDestinationType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMinorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enSourceOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDestinationOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_OffsetValueType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMajorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MajorLoopCountType" 
                                       type="INTEGER" value="1">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:ctr>
                          <d:ctr 
                                 name="dmaLogicChannelConfig_ScatterGatherArrayType_2" 
                                 type="IDENTIFIABLE">
                            <d:ctr 
                                   name="dmaLogicChannelConfig_ScatterGatherElementConfigType" 
                                   type="IDENTIFIABLE">
                              <d:var 
                                     name="dmaLogicChannelConfig_ScatterGatherElementNameType" 
                                     type="STRING" 
                                     value="DMA_LOGIC_CH_0_SGA_ELEMENT_2">
                                <a:a name="IMPORTER_INFO">
                                  <a:v>@DEF</a:v>
                                  <a:v>@CALC</a:v>
                                </a:a>
                              </d:var>
                              <d:var 
                                     name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" 
                                     type="BOOLEAN" value="false"/>
                              <d:ref 
                                     name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" 
                                     type="REFERENCE" 
                                     value="ASPath:/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_3">
                                <a:a name="ENABLE" value="true"/>
                              </d:ref>
                              <d:var 
                                     name="dmaLogicChannelConfig_enScatterGatherConfig" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferControlType" 
                                     type="IDENTIFIABLE">
                                <d:var name="dmaLogicChannelConfig_enStart" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_bandwidthControl" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_BWC_ENGINE_NO_STALL">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferSourceType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferDestinationType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMinorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enSourceOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDestinationOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_OffsetValueType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMajorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MajorLoopCountType" 
                                       type="INTEGER" value="1">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:ctr>
                          <d:ctr 
                                 name="dmaLogicChannelConfig_ScatterGatherArrayType_3" 
                                 type="IDENTIFIABLE">
                            <d:ctr 
                                   name="dmaLogicChannelConfig_ScatterGatherElementConfigType" 
                                   type="IDENTIFIABLE">
                              <d:var 
                                     name="dmaLogicChannelConfig_ScatterGatherElementNameType" 
                                     type="STRING" 
                                     value="DMA_LOGIC_CH_0_SGA_ELEMENT_3">
                                <a:a name="IMPORTER_INFO">
                                  <a:v>@DEF</a:v>
                                  <a:v>@CALC</a:v>
                                </a:a>
                              </d:var>
                              <d:var 
                                     name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ref 
                                     name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" 
                                     type="REFERENCE" 
                                     value="ASPath:/Mcl/Mcl/MclConfig/CHANNEL_FOR_ADC1/dmaLogicChannel_ConfigType/dmaLogicChannel_ScatterGatherConfigType/dmaLogicChannelConfig_ScatterGatherArrayType_4">
                                <a:a name="ENABLE" value="true"/>
                              </d:ref>
                              <d:var 
                                     name="dmaLogicChannelConfig_enScatterGatherConfig" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferControlType" 
                                     type="IDENTIFIABLE">
                                <d:var name="dmaLogicChannelConfig_enStart" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_bandwidthControl" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_BWC_ENGINE_NO_STALL">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferSourceType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferDestinationType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMinorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enSourceOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDestinationOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_OffsetValueType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMajorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MajorLoopCountType" 
                                       type="INTEGER" value="1">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:ctr>
                          <d:ctr 
                                 name="dmaLogicChannelConfig_ScatterGatherArrayType_4" 
                                 type="IDENTIFIABLE">
                            <d:ctr 
                                   name="dmaLogicChannelConfig_ScatterGatherElementConfigType" 
                                   type="IDENTIFIABLE">
                              <d:var 
                                     name="dmaLogicChannelConfig_ScatterGatherElementNameType" 
                                     type="STRING" 
                                     value="DMA_LOGIC_CH_0_SGA_ELEMENT_4">
                                <a:a name="IMPORTER_INFO">
                                  <a:v>@DEF</a:v>
                                  <a:v>@CALC</a:v>
                                </a:a>
                              </d:var>
                              <d:var 
                                     name="dmaLogicChannelConfig_LastElementLink_ScatterGatherType" 
                                     type="BOOLEAN" value="true"/>
                              <d:ref 
                                     name="dynamic_dmaLogicChannelConfig_BasicElementLink_ScatterGatherType" 
                                     type="REFERENCE" >
                                <a:a name="ENABLE" value="false"/>
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:ref>
                              <d:var 
                                     name="dmaLogicChannelConfig_enScatterGatherConfig" 
                                     type="BOOLEAN" value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferControlType" 
                                     type="IDENTIFIABLE">
                                <d:var name="dmaLogicChannelConfig_enStart" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDmaHalfMajorInterrupt" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_disDmaAutoHwReq" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_bandwidthControl" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_BWC_ENGINE_NO_STALL">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_ScatterGatherAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationStoreAddressType" 
                                       type="STRING" value="0U">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferSourceType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_SourceModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferDestinationType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationAddressType" 
                                       type="STRING" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationSignedOffsetType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationLastAddressAdjustmentType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaTransferConfig_TransferSizeType" 
                                       type="ENUMERATION" 
                                       value="DMA_IP_TRANSFER_SIZE_1_BYTE">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_DestinationModuloType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMinorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enSourceOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enDestinationOffset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_OffsetValueType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var 
                                       name="dmaLogicChannelConfig_enMinorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MinorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MinorLoopSizeType" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr 
                                     name="dmaLogicChannelConfig_TransferMajorLoopType" 
                                     type="IDENTIFIABLE">
                                <d:var 
                                       name="dmaLogicChannelConfig_enMajorLoopLinkCh" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:ref 
                                       name="dynamic_dmaLogicChannelConfig_MajorLoopLinkChValueType" 
                                       type="REFERENCE" >
                                  <a:a name="ENABLE" value="false"/>
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:ref>
                                <d:var 
                                       name="dmaLogicChannelConfig_MajorLoopCountType" 
                                       type="INTEGER" value="1">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:ctr>
                        </d:lst>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_CrcConfigType" 
                             type="IDENTIFIABLE">
                        <d:var name="dmaLogicChannel_HwCrcChId" 
                               type="ENUMERATION" value="DMA_IP_HW_CRC_0_CH_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="dmaLogicChannel_CrcMode" 
                               type="ENUMERATION" value="NORMAL_CRC_MODE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="dmaLogicChannel_CrcPolynomial" 
                               type="ENUMERATION" value="ETHERNET_CCITT32_CRC32">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="dmaLogicChannel_CrcInitialValue" 
                               type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="dmaLogicChannel_CrcEnInitSel" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="dmaLogicChannel_CrcEnLogic" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
