<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Platform" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Platform" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/TS_T40D11M50I0R0/Platform"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="GeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="PlatformDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformMcmConfigurable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformIpAPIsAvailable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformMulticoreSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformEnableVtorConfiguration" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformEnableIntCtrlConfiguration" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PlatformEnableMSIConfiguration" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="PlatformEcucPartitionRef"/>
              </d:ctr>
              <d:lst name="McmConfig" type="MAP"/>
              <d:lst name="IntCtrlConfig" type="MAP">
                <d:ctr name="IntCtrlConfig_0" type="IDENTIFIABLE">
                  <d:lst name="PlatformIsrConfig" type="MAP">
                    <d:ctr name="PlatformIsrConfig_0" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="Pcie_1_MSI_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_1" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="INT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_2" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="INT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_3" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="INT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_4" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="Pcie_0_MSI_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_5" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTI_0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_6" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTI_1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_7" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="MCM_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_8" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA0_0_15_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_9" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA0_16_31_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_10" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA0_ERR0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_11" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA1_0_15_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_12" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA1_16_31_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_13" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DMA1_ERR0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_14" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_15" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_16" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_17" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_18" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT4_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_19" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT5_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_20" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT6_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_21" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SWT7_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_22" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_23" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_24" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_25" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_26" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM4_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_27" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM5_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_28" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM6_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_29" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="STM7_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_30" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="QSPI0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_31" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="QSPI1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_32" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="QSPI2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_33" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="STCU2_LBIST_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_34" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="USDHC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_35" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN0_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_36" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN0_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_37" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN0_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_38" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN0_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_39" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN1_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_40" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN1_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_41" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN1_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_42" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN1_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_43" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN2_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_44" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN2_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_45" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN2_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_46" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN2_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_47" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN3_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_48" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN3_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_49" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN3_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_50" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN3_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_51" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="PIT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_52" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="PIT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_53" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="FTM0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_54" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="FTM1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_55" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_Common_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_56" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH0_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_57" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH0_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_58" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH1_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_59" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH1_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_60" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH2_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_61" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH2_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_62" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH3_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_63" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH3_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_64" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH4_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_65" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC0_CH4_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_66" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SAR_ADC0_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_67" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SAR_ADC1_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_68" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_NCERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_69" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_CERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_70" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_CH0_RX_FIFO_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_71" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_CH1_RX_FIFO_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_72" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_WKUP_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_73" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_STATUS_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_74" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_CMBERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_75" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_TX_BUFF_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_76" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_RX_BUFF_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_77" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FLEXRAY0_MODULE_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_78" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LINFLEXD0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_79" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LINFLEXD1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_80" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_81" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_82" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_83" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_84" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI4_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_85" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SPI5_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_86" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="I2C0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_87" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="I2C1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_88" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MC_RGM_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_89" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FCCU_ALARM_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_90" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FCCU_MISC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_91" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="SBSW_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_92" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU0_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_93" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU0_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_94" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU0_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_95" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU1_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_96" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU1_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_97" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU1_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_98" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU2_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_99" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU2_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_100" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU2_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_101" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU3_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_102" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU3_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_103" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="HSE_MU3_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_104" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DDR0_SCRUB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_105" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="DDR0_PHY_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_106" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTU_FIFO_FULL_EMPTY_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_107" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTU_M_RELOAD_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_108" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTU_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_109" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="TMU_ALARM_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_110" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_ORED_DMA_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_111" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_LINK_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_112" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_AXI_MSI_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_113" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_PHY_LDOWN_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_114" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_PHY_LUP_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_115" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_INTA_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_116" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_INTB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_117" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_INTC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_118" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_INTD_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_119" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_MISC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_120" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_PCS_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_121" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE0_TLP_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_122" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CORTEX_A53_ERR_L2RAM_CLUSTER0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_123" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CORTEX_A53_ERR_AXI_CLUSTER0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_124" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CORTEX_A53_ERR_L2RAM_CLUSTER1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_125" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CORTEX_A53_ERR_AXI_CLUSTER1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_126" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" value="JDC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_127" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FASTDMA_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_128" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="FASTDMA_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_129" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_0_INT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_130" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_0_INT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_131" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_0_INT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_132" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_0_INT3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_133" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_1_INT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_134" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_1_INT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_135" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_1_INT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_136" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_1_INT3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_137" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_2_INT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_138" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_2_INT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_139" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_2_INT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_140" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_2_INT3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_141" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_3_INT0_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_142" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_3_INT1_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_143" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_3_INT2_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_144" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="MIPICSI2_3_INT3_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_145" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SPT_DSP_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_146" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SPT_EVENT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_147" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SPT_ECS_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_148" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SPT_DMA_COMPL_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_149" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN4_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_150" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN4_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_151" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN4_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_152" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN4_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_153" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN5_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_154" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN5_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_155" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN5_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_156" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN5_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_157" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN6_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_158" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN6_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_159" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN6_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_160" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN6_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_161" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN7_ORED_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_162" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN7_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_163" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN7_ORED_0_7_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_164" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CAN7_ORED_8_127_MB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_165" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_ORED_DMA_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_166" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_LINK_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_167" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_AXI_MSI_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_168" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_PHY_LDOWN_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_169" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_PHY_LUP_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_170" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_INTA_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_171" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_INTB_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_172" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_INTC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_173" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_INTD_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_174" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_MISC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_175" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_PCS_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_176" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="PCIE1_TLP_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_177" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_Common_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_178" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH0_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_179" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH0_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_180" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH1_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_181" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH1_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_182" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH2_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_183" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH2_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_184" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH3_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_185" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH3_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_186" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH4_TX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_187" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="GMAC1_CH4_RX_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_188" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="CTE_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_189" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX1_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_190" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX0_ERR_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_191" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX0_ORED_FUNC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_192" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX0_SOFT_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_193" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="SIUL2_1_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_194" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX1_ORED_FUNC_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="PlatformIsrConfig_195" type="IDENTIFIABLE">
                      <d:var name="IsrName" type="ENUMERATION" 
                             value="LAX1_SOFT_INT_IRQn">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IsrEnabled" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IsrHandler" type="FUNCTION-NAME" 
                             value="undefined_handler">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:lst>
                  <d:ref name="PlatformNvicEcucPartitionRef" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                  </d:ref>
                  <d:var name="PlatformVtorAddressConfig" type="INTEGER" 
                         value="0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                </d:ctr>
              </d:lst>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="5"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="43">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
