1. Example Description
    The purpose of this demo application is to present basic usage of the ADC_SAR and CTU IP Driver.
    1.1 The application software functionality
    The example uses the software trigger to trigger ADC normal and injected conversions, also software trigger from CTU.
    The used ADC channel is BandGap (~1.15V corresponding to 2626 with 12 bits resolution at assumed 1.8V reference). The raw read ADC data will be compared
    with provided value. If the result is correct, the program will go through the While loop of checking this result in Adc user notification (AdcEndOfChainNotif).
    The example is divided in 2 parts:
    - Part 1: The sequences of conversions are triggered using SW triggering from ADC with 2 modes: normal and injected modes
    - Part 2: The sequences of conversion is triggered using SW triggering from CTU, the ADC results are stored in FIFO index 0
    Both parts of the example make use of ADC and CTU interrupts.
    - Initialize the ADC/CTU modules.
    - Enable ADC/CTU IRQs in interrupt controller.
    - Start the ADC conversions by normal/injected software trigger from ADC
    - Read raw ADC data conversion and compare with provided value
    - Configure CTU with single conversion and result stored in FIFO.
    - Start the ADC conversion by software trigger from CTU
    - Read raw ADC data conversion

    1.2 How the example works
    - If all conditions are correct, the code goes to the end of program in the For loop checking exit_code variable, without stuck in any other While loops.

2. Installation steps
    2.1 Hardware installation
        2.1.1 Supported boards
            - EVB: S32G-VNP-RDB3 PCB 53060 RevC SCH RevF, S32G3 silicon (Rev 1.1)
            - Motherboard: S32GRV-PLATEVB PCB RevA SCH RevB
            - Daughter card: S32G-PROCEVB-S PCB RevX3 SCH RevB1
            - Silicon: P32G399AACVUC SBAU2235 1P72B TIAUSBA
        2.1.2 Connections
            - N/A
        2.1.3 Debugger
            The debugger must be connected to J48 20-pin JTAG Cortex Debug connector.
    2.2 Software installation
        2.2.1 Importing the S32 Design Studio project
            After opening S32 Design Studio, go to "File -> New -> S32DS Project From Example" and select this example. Then click on "Finish".
            The project should now be copied into you current workspace.

3. Generating, building and running the example application
    3.1 Generating the S32 configuration
        Before running the example a configuration needs to be generated.  First go to Project Explorer View in S32 DS and select the current project. Select the "S32 Configuration Tool" menu then click on the desired configuration tool (Pins, Cocks, Peripherals etc...). Clicking on any one of those will generate all the components. Make the desired changes(if any) then click on the "S32 Configuration Tool->Update Code" button.
    3.2 Compiling the application
        Select the configuration to be built: RAM (Debug_RAM) by left clicking on the downward arrow corresponding to the build button in eclipse.
        Use Project > Build to build the project.
        Wait for the build action to be completed before continuing to the next step. Check the compiler console for error messages; upon completion, the *.elf binary file
        should be created.
    3.3 Running the application on the board
        Go to Run and select Debug Configurations. There will be a debug configuration for this project:

        Configuration Name                                   Description
        ---------------------------------------------------------------------
        Adc_Sar_Ctu_example_S32G399A_M7_Debug_RAM_S32Debug | Debug the RAM configuration using S32 Debugger

        Select the desired debug configuration and click on Launch. Now the perspective will change to the Debug Perspective.
        Use the controls to control the program flow.
